{"name": "WebODM", "version": "2.9.0", "description": "User-friendly, extendable application and API for processing aerial imagery.", "main": "index.js", "scripts": {"test": "python manage.py test app.tests.test_generate_ui_mocks && jest", "qtest": "jest"}, "repository": {"type": "git", "url": "git+https://github.com/OpenDroneMap/WebODM.git"}, "keywords": ["opendronemap"], "author": "<PERSON><PERSON>", "license": "AGPL-3.0", "bugs": {"url": "https://github.com/OpenDroneMap/WebODM/issues"}, "homepage": "https://github.com/OpenDroneMap/WebODM#readme", "dependencies": {"@babel/core": "^7.0.0-beta.54", "@babel/plugin-proposal-class-properties": "^7.0.0-beta.54", "@babel/plugin-syntax-class-properties": "^7.0.0-beta.54", "@babel/preset-env": "^7.0.0-beta.54", "@babel/preset-react": "^7.0.0-beta.54", "@babel/runtime": "^7.4.5", "async": "^2.1.2", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^23.4.0", "babel-loader": "^8.0.0-beta.4", "clipboard": "^1.7.1", "css-loader": "^0.25.0", "d3": "^3.5.5", "enzyme": "^3.3.0", "cheerio": "1.0.0-rc.6", "enzyme-adapter-react-16": "^1.15.1", "exifr": "^6.0.0", "fbemitter": "^2.1.1", "file-loader": "^0.9.0", "file-saver": "^2.0.2", "gl-matrix": "^2.3.2", "history": "^4.7.2", "immutability-helper": "^2.0.0", "jest": "^23.4.1", "json-loader": "^0.5.4", "leaflet": "1.3.1", "leaflet-fullscreen": "^1.0.2", "object.values": "^1.0.3", "proj4": "^2.4.3", "qrcode.react": "^0.7.2", "raw-loader": "^0.5.1", "rbush": "^3.0.1", "react": "^16.4.0", "react-dom": "^16.4.0", "react-router": "^4.1.1", "react-router-dom": "^4.1.1", "react-test-renderer": "^16.14.0", "regenerator-runtime": "^0.11.0", "sass": "^1.22.7", "sass-loader": "13.3.2", "shpjs": "^3.4.2", "sinon": "^4.0.0", "statuses": "^1.3.1", "style-loader": "^0.13.1", "tween.js": "^16.6.0", "url-loader": "^0.5.7", "webpack": "5.89.0", "webpack-bundle-tracker": "0.4.3", "webpack-livereload-plugin": "3.0.2", "mini-css-extract-plugin": "1.6.2", "buffer": "^6.0.3"}}