# Generated by Django 2.1.5 on 2019-02-20 18:42

import django.contrib.postgres.fields.jsonb
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('nodeodm', '0005_auto_20190115_1346'),
    ]

    operations = [
        migrations.AlterField(
            model_name='processingnode',
            name='available_options',
            field=django.contrib.postgres.fields.jsonb.JSONField(default=dict, help_text='Description of the options that can be used for processing'),
        ),
        migrations.AlterField(
            model_name='processingnode',
            name='label',
            field=models.CharField(blank=True, default='', help_text='Optional label for this node. When set, this label will be shown instead of the hostname:port name.', max_length=255),
        ),
    ]
