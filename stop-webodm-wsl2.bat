@echo off
setlocal enabledelayedexpansion

echo ========================================
echo Stopping WebODM in WSL2
echo ========================================
echo.

echo Stopping WebODM containers...
wsl -d Ubuntu bash -c "cd /home/<USER>/webodm && ./webodm.sh down"

echo.
echo ========================================
echo WebODM has been stopped
echo ========================================
echo.
echo To start WebODM again, run: start-webodm-wsl2.bat
echo.
pause
