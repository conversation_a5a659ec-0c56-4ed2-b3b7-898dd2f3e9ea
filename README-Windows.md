# WebODM Windows Setup Guide

This guide explains how to run WebODM on Windows using the provided batch files.

## Prerequisites

Before running WebODM on Windows, ensure you have the following installed:

1. **Docker Desktop for Windows**
   - Download from: https://www.docker.com/products/docker-desktop
   - Make sure Linux containers are enabled (Switch to Linux Containers...)
   - Allocate sufficient resources:
     - CPUs: At least 2 (default), more recommended
     - RAM: At least 4GB, 16GB recommended
   - Configure storage location in Settings → Advanced → Images & Volumes

2. **Git for Windows**
   - Download from: https://git-scm.com/downloads
   - Required for cloning the repository and updates

## Quick Start

1. **Clone the WebODM repository:**
   ```cmd
   git clone https://github.com/OpenDroneMap/WebODM --config core.autocrlf=input --depth 1
   cd WebODM
   ```

2. **Start WebODM using the Windows batch script:**
   ```cmd
   webodm.bat start
   ```

3. **Access WebODM:**
   - Open your web browser and navigate to: http://localhost:8000
   - Create an admin account when prompted

4. **Stop WebODM:**
   ```cmd
   webodm.bat stop
   ```

## Available Commands

The `webodm.bat` script supports the same commands as the original `webodm.sh`:

### Basic Commands
- `webodm.bat start` - Start WebODM
- `webodm.bat stop` - Stop WebODM
- `webodm.bat restart` - Restart WebODM
- `webodm.bat down` - Stop and remove all containers
- `webodm.bat update` - Update WebODM to the latest version
- `webodm.bat rebuild` - Rebuild all containers from scratch

### Management Commands
- `webodm.bat checkenv` - Check if Docker is properly installed
- `webodm.bat resetadminpassword "newpassword"` - Reset admin password

### Options
All the same options from the original script are supported:

- `--port <port>` - Set custom port (default: 8000)
- `--hostname <hostname>` - Set hostname (default: localhost)
- `--media-dir <path>` - Custom media storage directory
- `--db-dir <path>` - Custom database storage directory
- `--default-nodes <number>` - Number of processing nodes (default: 1)
- `--detached` - Run in background mode
- `--dev` - Enable development mode
- `--ssl` - Enable SSL with Let's Encrypt
- `--ssl-key <path>` - Manual SSL key file
- `--ssl-cert <path>` - Manual SSL certificate file

### Examples

**Start with custom port:**
```cmd
webodm.bat start --port 9000
```

**Start in development mode:**
```cmd
webodm.bat start --dev
```

**Start with multiple processing nodes:**
```cmd
webodm.bat start --default-nodes 3
```

**Start in detached mode (background):**
```cmd
webodm.bat start --detached
```

## GPU Support (Experimental)

GPU acceleration is experimental on Windows. To enable it:

1. Install WSL2 (Windows Subsystem for Linux)
2. Install NVIDIA drivers that support WSL2
3. Use the `--gpu` flag:
   ```cmd
   webodm.bat start --gpu
   ```

## File Storage

By default, WebODM stores data in Docker volumes:
- **Media files** (processing results): `webodm_appmedia` volume
- **Database**: `webodm_dbdata` volume

To use custom directories on your Windows filesystem:
```cmd
webodm.bat start --media-dir "C:\WebODM\media" --db-dir "C:\WebODM\database"
```

## Troubleshooting

### Common Issues

1. **"Docker is not installed or not in PATH"**
   - Ensure Docker Desktop is installed and running
   - Restart your command prompt after installing Docker

2. **"Cannot find webapp docker container"**
   - Make sure WebODM is running with `webodm.bat start`
   - Check if containers are running: `docker ps`

3. **Port already in use**
   - Use a different port: `webodm.bat start --port 9000`
   - Or stop the service using port 8000

4. **Out of memory errors**
   - Increase Docker Desktop memory allocation in Settings
   - Recommended: 8GB+ for processing large datasets

5. **SSL certificate issues**
   - For Let's Encrypt SSL, ensure ports 80 and 443 are open
   - Use a proper domain name, not localhost

### Performance Tips

1. **Allocate more resources to Docker:**
   - Go to Docker Desktop Settings → Resources
   - Increase CPU and Memory allocation

2. **Use SSD storage:**
   - Store media and database on SSD for better performance

3. **Close unnecessary applications:**
   - WebODM can be resource-intensive during processing

## Differences from Linux Version

The Windows batch files provide the same functionality as the Linux shell scripts but with Windows-specific adaptations:

- Uses Windows batch syntax instead of bash
- Uses PowerShell for some operations (HTTP requests, text processing)
- Uses Windows-specific commands for process management
- Handles Windows file paths correctly

## Getting Help

If you encounter issues:

1. Check the [WebODM Community Forum](http://community.opendronemap.org/c/webodm)
2. Report bugs on [GitHub Issues](https://github.com/OpenDroneMap/WebODM/issues)
3. Consult the main [README.md](README.md) for general WebODM documentation

## Native Installation

For advanced users who prefer running WebODM natively on Windows (not recommended), you'll need to install:
- PostgreSQL with PostGIS
- Python 3.6+
- Node.js
- GDAL
- Redis
- And other dependencies listed in the main README

The Docker approach using these batch files is much simpler and recommended for most users.
