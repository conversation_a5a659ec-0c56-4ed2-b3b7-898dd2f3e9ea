@echo off
setlocal enabledelayedexpansion

echo ========================================
echo Docker Desktop Debug and Recovery Script
echo ========================================
echo.
echo Current issue: Docker Desktop services failing to start
echo Error: "running com.docker.build: exit status 1"
echo.

echo [1/10] Checking current Docker Desktop status...
tasklist /FI "IMAGENAME eq Docker Desktop.exe" 2>nul | find /I "Docker Desktop.exe" >nul
if errorlevel 1 (
    echo Docker Desktop is not running
) else (
    echo Docker Desktop process is running
)

tasklist /FI "IMAGENAME eq com.docker.backend.exe" 2>nul | find /I "com.docker.backend.exe" >nul
if errorlevel 1 (
    echo Docker backend is not running
) else (
    echo Docker backend process is running
)
echo.

echo [2/10] Completely stopping all Docker processes...
taskkill /f /im "Docker Desktop.exe" >nul 2>&1
taskkill /f /im "com.docker.backend.exe" >nul 2>&1
taskkill /f /im "com.docker.cli.exe" >nul 2>&1
taskkill /f /im "docker.exe" >nul 2>&1
taskkill /f /im "dockerd.exe" >nul 2>&1
taskkill /f /im "vpnkit.exe" >nul 2>&1
taskkill /f /im "com.docker.proxy.exe" >nul 2>&1

echo Waiting for processes to terminate...
timeout /t 10 /nobreak >nul
echo.

echo [3/10] Stopping WSL2 and Hyper-V services...
wsl --shutdown
net stop "HV Host Service" >nul 2>&1
net stop "Hyper-V Host Compute Service" >nul 2>&1
timeout /t 5 /nobreak >nul
echo.

echo [4/10] Cleaning Docker Desktop data directories...
set DOCKER_DATA=%LOCALAPPDATA%\Docker
set DOCKER_ROAMING=%APPDATA%\Docker

if exist "%DOCKER_DATA%\log" (
    echo Clearing Docker logs...
    del /q "%DOCKER_DATA%\log\*" >nul 2>&1
)

if exist "%DOCKER_DATA%\pki" (
    echo Clearing Docker PKI cache...
    rmdir /s /q "%DOCKER_DATA%\pki" >nul 2>&1
)

if exist "%DOCKER_ROAMING%\contexts" (
    echo Clearing Docker contexts...
    rmdir /s /q "%DOCKER_ROAMING%\contexts" >nul 2>&1
)
echo.

echo [5/10] Resetting WSL2 Docker integration...
wsl --unregister docker-desktop >nul 2>&1
wsl --unregister docker-desktop-data >nul 2>&1
echo WSL2 Docker distributions unregistered
echo.

echo [6/10] Starting Hyper-V services...
net start "HV Host Service" >nul 2>&1
net start "Hyper-V Host Compute Service" >nul 2>&1
echo.

echo [7/10] Starting Docker Desktop...
REM Find Docker Desktop executable
set DOCKER_EXE=
if exist "%ProgramFiles%\Docker\Docker\Docker Desktop.exe" (
    set DOCKER_EXE=%ProgramFiles%\Docker\Docker\Docker Desktop.exe
) else if exist "%LOCALAPPDATA%\Programs\Docker\Docker\Docker Desktop.exe" (
    set DOCKER_EXE=%LOCALAPPDATA%\Programs\Docker\Docker\Docker Desktop.exe
) else (
    echo ERROR: Cannot find Docker Desktop executable
    echo Please reinstall Docker Desktop
    goto :manual_fix
)

echo Starting Docker Desktop from: !DOCKER_EXE!
start "" "!DOCKER_EXE!"
echo.

echo [8/10] Waiting for Docker Desktop to initialize...
echo This may take 2-5 minutes depending on your system...
echo.

set WAIT_COUNT=0
:wait_docker
timeout /t 15 /nobreak >nul
set /a WAIT_COUNT+=1

echo Attempt !WAIT_COUNT!/20 - Checking Docker status...
docker version >nul 2>&1
if not errorlevel 1 (
    echo Docker Desktop is now running!
    goto :test_docker
)

if !WAIT_COUNT! geq 20 (
    echo Timeout waiting for Docker Desktop to start
    goto :manual_fix
)

echo Still waiting...
goto :wait_docker

:test_docker
echo.
echo [9/10] Testing Docker functionality...
docker run --rm hello-world >nul 2>&1
if errorlevel 1 (
    echo Docker is running but containers fail to start
    echo This may be a deeper issue
    goto :advanced_fix
) else (
    echo Docker containers work correctly!
)
echo.

echo [10/10] Testing WebODM compatibility...
docker run --rm busybox echo "test" >nul 2>&1
if errorlevel 1 (
    echo Basic containers still have issues
    goto :advanced_fix
) else (
    echo Basic containers work!
)

echo.
echo ========================================
echo SUCCESS: Docker Desktop recovered!
echo ========================================
echo.
echo You can now try running WebODM:
echo   webodm.bat start
echo.
goto :end

:advanced_fix
echo.
echo ========================================
echo ADVANCED FIX REQUIRED
echo ========================================
echo.
echo Docker Desktop started but containers still fail.
echo Trying advanced recovery steps...
echo.

echo Resetting Docker Desktop to factory defaults...
echo This will remove all containers, images, and volumes!
echo.
set /p CONFIRM=Continue with factory reset? (y/N): 
if /i not "!CONFIRM!"=="y" goto :manual_fix

REM Stop Docker Desktop again
taskkill /f /im "Docker Desktop.exe" >nul 2>&1
timeout /t 5 /nobreak >nul

REM Remove Docker Desktop data
if exist "%LOCALAPPDATA%\Docker" (
    echo Removing Docker Desktop data...
    rmdir /s /q "%LOCALAPPDATA%\Docker" >nul 2>&1
)

if exist "%APPDATA%\Docker" (
    echo Removing Docker Desktop settings...
    rmdir /s /q "%APPDATA%\Docker" >nul 2>&1
)

echo Factory reset completed. Starting Docker Desktop...
start "" "!DOCKER_EXE!"

echo Waiting for Docker Desktop to initialize after reset...
timeout /t 60 /nobreak >nul

docker version >nul 2>&1
if errorlevel 1 (
    echo Factory reset failed to resolve the issue
    goto :manual_fix
) else (
    echo Factory reset successful!
    echo.
    echo You can now try running WebODM:
    echo   webodm.bat start
    goto :end
)

:manual_fix
echo.
echo ========================================
echo MANUAL INTERVENTION REQUIRED
echo ========================================
echo.
echo Automatic recovery failed. Please try these manual steps:
echo.
echo 1. COMPLETE DOCKER DESKTOP REINSTALL:
echo    - Uninstall Docker Desktop from Windows Settings
echo    - Download fresh installer from https://docker.com
echo    - Install with default settings
echo    - Restart Windows
echo.
echo 2. CHECK WINDOWS FEATURES:
echo    - Open "Turn Windows features on or off"
echo    - Ensure these are enabled:
echo      * Windows Subsystem for Linux
echo      * Virtual Machine Platform
echo      * Hyper-V (if available)
echo    - Restart Windows after changes
echo.
echo 3. CHECK BIOS SETTINGS:
echo    - Ensure Virtualization is enabled in BIOS
echo    - Look for VT-x, AMD-V, or SVM settings
echo.
echo 4. ALTERNATIVE - USE DOCKER TOOLBOX:
echo    - If Docker Desktop continues to fail
echo    - Consider Docker Toolbox as alternative
echo.
echo 5. SYSTEM REQUIREMENTS:
echo    - Windows 10/11 Pro, Enterprise, or Education
echo    - At least 4GB RAM
echo    - SLAT-capable CPU
echo.

:end
echo.
pause
