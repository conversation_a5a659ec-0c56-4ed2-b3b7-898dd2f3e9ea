[Unit]
Description=Start WebODM OpenDroneMap Service Container
Requires=docker.service
After=docker.service

[Service]
Type=forking
User=odm
Group=odm
WorkingDirectory=/opt/WebODM
Environment=PATH=/home/<USER>/.local/bin:/usr/sbin:/usr/bin:/sbin:/bin
ExecStart=/bin/bash -c 'screen -dmS webodm /opt/WebODM/webodm.sh start'
ExecStop=/bin/bash -c '/opt/WebODM/webodm.sh stop'
Restart=on-failure

[Install]
WantedBy=multi-user.target
