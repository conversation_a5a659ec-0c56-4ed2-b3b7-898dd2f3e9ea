[Unit]
Description=Start WebODM Gunicorn Service Container
Requires=webodm-nginx.service postgresql.service redis.service
After=webodm-nginx.service postgresql.service redis.service

[Service]
Type=simple
User=odm
Group=odm
PIDFile=/run/webodm-gunicorn.pid
WorkingDirectory=/webodm
ExecStart=/webodm/python3-venv/bin/gunicorn webodm.wsgi --bind unix:/webodm/gunicorn.sock --timeout 300000 --max-requests 250 --preload
ExecStop=/bin/kill -s QUIT $MAINPID
Restart=on-failure

[Install]
WantedBy=multi-user.target