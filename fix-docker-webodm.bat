@echo off
setlocal enabledelayedexpansion

echo ========================================
echo WebODM Docker Fix Script
echo ========================================
echo.
echo This script will attempt to fix the "exec format error" issue
echo by trying multiple solutions systematically.
echo.

REM Test if basic Docker works
echo [1/8] Testing basic Docker functionality...
docker run --rm busybox echo "Docker basic test" >nul 2>&1
if errorlevel 1 (
    echo ERROR: Basic Docker functionality is broken
    echo Please restart Docker Desktop and try again
    pause
    exit /b 1
)
echo Docker basic functionality: OK
echo.

REM Test if the issue is with entrypoint scripts
echo [2/8] Testing Redis container (known to have entrypoint issues)...
docker run --rm redis:7.0.10 echo "test" >nul 2>&1
if errorlevel 1 (
    echo Redis container has exec format error - this is the known issue
) else (
    echo Redis container works fine - issue may be resolved
    goto :test_webodm
)
echo.

REM Try to fix WSL2 integration
echo [3/8] Checking WSL2 integration...
wsl --list --verbose
echo.
echo Restarting WSL2...
wsl --shutdown
timeout /t 5 /nobreak >nul
echo WSL2 restarted
echo.

REM Set explicit platform
echo [4/8] Setting explicit Docker platform...
set DOCKER_DEFAULT_PLATFORM=linux/amd64
set COMPOSE_DOCKER_CLI_BUILD=1
set DOCKER_BUILDKIT=1
echo Platform set to: %DOCKER_DEFAULT_PLATFORM%
echo.

REM Remove potentially corrupted images
echo [5/8] Removing potentially corrupted WebODM images...
docker rmi opendronemap/webodm_webapp:latest 2>nul
docker rmi opendronemap/webodm_db:latest 2>nul
docker rmi redis:7.0.10 2>nul
docker rmi opendronemap/nodeodm:latest 2>nul
echo Old images removed
echo.

REM Pull fresh images with explicit platform
echo [6/8] Pulling fresh images with explicit platform...
echo Pulling Redis...
docker pull --platform linux/amd64 redis:7.0.10
echo Pulling WebODM Database...
docker pull --platform linux/amd64 opendronemap/webodm_db:latest
echo Pulling WebODM WebApp...
docker pull --platform linux/amd64 opendronemap/webodm_webapp:latest
echo Pulling NodeODM...
docker pull --platform linux/amd64 opendronemap/nodeodm:latest
echo All images pulled successfully
echo.

REM Test Redis again
echo [7/8] Testing Redis container after fresh pull...
docker run --rm redis:7.0.10 echo "Redis test after fix" >nul 2>&1
if errorlevel 1 (
    echo Redis still has issues - trying alternative approach...
    goto :alternative_approach
) else (
    echo Redis container now works!
    goto :test_webodm
)

:alternative_approach
echo.
echo [7b/8] Trying alternative Redis image...
docker pull --platform linux/amd64 redis:alpine
docker run --rm redis:alpine echo "Alternative Redis test" >nul 2>&1
if errorlevel 1 (
    echo Alternative Redis also fails - this is a deeper Docker issue
    goto :docker_reset_recommendation
) else (
    echo Alternative Redis works - updating docker-compose to use redis:alpine
    goto :update_compose_redis
)

:update_compose_redis
echo Updating docker-compose.yml to use redis:alpine...
powershell -Command "(Get-Content 'docker-compose.yml') -replace 'redis:7.0.10', 'redis:alpine' | Set-Content 'docker-compose.yml'"
echo Updated docker-compose.yml to use redis:alpine
goto :test_webodm

:test_webodm
echo.
echo [8/8] Testing WebODM startup...
echo Starting WebODM with fixed configuration...
set DOCKER_DEFAULT_PLATFORM=linux/amd64
webodm.bat start --detached
if errorlevel 1 (
    echo WebODM startup failed
    goto :docker_reset_recommendation
) else (
    echo WebODM startup successful!
    echo.
    echo Waiting 30 seconds for services to initialize...
    timeout /t 30 /nobreak >nul
    
    echo Checking container status...
    docker ps
    echo.
    echo WebODM should be accessible at: http://localhost:8000
    echo.
    echo Fix completed successfully!
    pause
    exit /b 0
)

:docker_reset_recommendation
echo.
echo ========================================
echo DOCKER RESET REQUIRED
echo ========================================
echo.
echo The exec format error persists despite all fixes.
echo This indicates a fundamental Docker Desktop issue.
echo.
echo RECOMMENDED ACTIONS:
echo.
echo 1. RESET DOCKER DESKTOP:
echo    - Open Docker Desktop
echo    - Go to Settings ^> Troubleshoot
echo    - Click "Reset to factory defaults"
echo    - This will remove all containers, images, and volumes
echo.
echo 2. ALTERNATIVE - REINSTALL DOCKER DESKTOP:
echo    - Uninstall Docker Desktop completely
echo    - Download fresh installer from docker.com
echo    - Reinstall with default settings
echo.
echo 3. CHECK WINDOWS FEATURES:
echo    - Ensure "Windows Subsystem for Linux" is enabled
echo    - Ensure "Virtual Machine Platform" is enabled
echo    - Restart Windows after enabling these features
echo.
echo 4. AFTER RESET/REINSTALL:
echo    - Run this script again
echo    - Or run: webodm.bat start
echo.
echo ========================================
pause
exit /b 1
