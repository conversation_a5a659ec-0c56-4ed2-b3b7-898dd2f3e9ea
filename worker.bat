@echo off
setlocal enabledelayedexpansion

REM WebODM Windows Worker Script
REM Equivalent to worker.sh for Windows systems

set SCRIPT_DIR=%~dp0
cd /d "%SCRIPT_DIR%"

REM Function to show usage
if "%~1"=="" goto show_usage
if "%~1"=="help" goto show_usage
if "%~1"=="--help" goto show_usage

goto %~1 2>nul || goto show_usage

:show_usage
echo Usage: %~nx0 ^<command^>
echo.
echo This program manages the background worker processes. WebODM requires at least one background process worker to be running at all times.
echo.
echo Command list:
echo     start                Start background worker
echo     scheduler start      Start background worker scheduler
echo     scheduler stop       Stop background worker scheduler
exit /b 0

:check_command
set CHECK_MSG_PREFIX=Checking for %~1... 
set CHECK_MSG_RESULT=OK
set NOT_FOUND=false

where %~1 >nul 2>&1
if errorlevel 1 (
    set NOT_FOUND=true
    set CHECK_MSG_RESULT=can't find %~1! Check that the program is installed and in your PATH. %~2
)

echo %CHECK_MSG_PREFIX% %CHECK_MSG_RESULT%
if "%NOT_FOUND%"=="true" exit /b 1
goto :eof

:environment_check
call :check_command "celery" "Run 'pip install -U celery'"
if errorlevel 1 exit /b 1

if not defined WO_BROKER (
    echo WO_BROKER environment variable is not set. Defaulting to redis://localhost
    set WO_BROKER=redis://localhost
)

REM Set web concurrency if not defined
if not defined WEB_CONCURRENCY (
    REM Get number of processors (Windows equivalent of nproc)
    set /a WEB_CONCURRENCY=%NUMBER_OF_PROCESSORS%*2+1
)
goto :eof

:start
call :environment_check
if errorlevel 1 exit /b 1

echo Starting worker using broker at %WO_BROKER%
celery -A worker worker --autoscale %WEB_CONCURRENCY%,2 --max-tasks-per-child 1000 --loglevel=warn >nul
goto :eof

:start_scheduler
call :stop_scheduler

if not exist "celerybeat.pid" (
    start /b celery -A worker beat
    echo Scheduler started.
) else (
    echo Scheduler already running (celerybeat.pid exists).
)
goto :eof

:stop_scheduler
if exist "celerybeat.pid" (
    set /p PID=<celerybeat.pid
    taskkill /PID !PID! /F >nul 2>&1
    del celerybeat.pid >nul 2>&1
    echo Scheduler has shutdown.
) else (
    echo Scheduler is not running.
)
goto :eof

:scheduler
if "%~2"=="start" (
    call :environment_check
    if errorlevel 1 exit /b 1
    call :start_scheduler
) else if "%~2"=="stop" (
    call :environment_check
    if errorlevel 1 exit /b 1
    call :stop_scheduler
) else (
    goto show_usage
)
goto :eof
