@echo off
setlocal enabledelayedexpansion

echo ========================================
echo Docker Desktop Restart Script
echo ========================================
echo.
echo This script will restart Docker Desktop to fix the 500 Internal Server Error
echo.

echo [1/5] Stopping Docker Desktop...
taskkill /f /im "Docker Desktop.exe" >nul 2>&1
taskkill /f /im "com.docker.backend.exe" >nul 2>&1
taskkill /f /im "com.docker.cli.exe" >nul 2>&1
taskkill /f /im "docker.exe" >nul 2>&1
taskkill /f /im "dockerd.exe" >nul 2>&1

echo Waiting for Docker Desktop to fully stop...
timeout /t 10 /nobreak >nul

echo [2/5] Stopping WSL2...
wsl --shutdown
timeout /t 5 /nobreak >nul

echo [3/5] Starting Docker Desktop...
REM Try common Docker Desktop installation paths
if exist "%ProgramFiles%\Docker\Docker\Docker Desktop.exe" (
    start "" "%ProgramFiles%\Docker\Docker\Docker Desktop.exe"
) else if exist "%LOCALAPPDATA%\Programs\Docker\Docker\Docker Desktop.exe" (
    start "" "%LOCALAPPDATA%\Programs\Docker\Docker\Docker Desktop.exe"
) else (
    echo ERROR: Could not find Docker Desktop executable
    echo Please start Docker Desktop manually
    pause
    exit /b 1
)

echo [4/5] Waiting for Docker Desktop to start...
echo This may take 1-2 minutes...

:wait_loop
timeout /t 10 /nobreak >nul
docker version >nul 2>&1
if errorlevel 1 (
    echo Still waiting for Docker Desktop...
    goto wait_loop
)

echo Docker Desktop is now running!

echo [5/5] Testing Docker functionality...
docker run --rm busybox echo "Docker test successful" >nul 2>&1
if errorlevel 1 (
    echo ERROR: Docker still not working properly
    echo Please check Docker Desktop manually
    pause
    exit /b 1
)

echo.
echo ========================================
echo Docker Desktop restart completed successfully!
echo ========================================
echo.
echo You can now try running WebODM:
echo   webodm.bat start
echo.
pause
