@echo off
setlocal enabledelayedexpansion

echo ========================================
echo Starting WebODM (Optimized)
echo ========================================
echo.

echo [1/2] Starting optimized WebODM in WSL2...
wsl -d Ubuntu bash -c "/home/<USER>/start-webodm-optimized.sh"

echo.
echo [2/2] Final status check...
wsl -d Ubuntu bash -c "cd /home/<USER>/webodm && docker ps --format 'table {{.Names}}\t{{.Status}}\t{{.Ports}}'"

echo.
echo ========================================
echo WebODM Optimized Startup Complete!
echo ========================================
echo.
echo WebODM is now running with:
echo - 8GB memory limit for WSL2
echo - Optimized Docker settings
echo - Enhanced startup validation
echo - Automatic container cleanup
echo.
echo Access WebODM at: http://localhost:8000
echo.
echo To monitor WebODM health: check-webodm-health.bat
echo To stop WebODM: stop-webodm-wsl2.bat
echo.
pause
