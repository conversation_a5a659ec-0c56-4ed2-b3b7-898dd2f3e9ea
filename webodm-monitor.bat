@echo off
setlocal enabledelayedexpansion

echo ========================================
echo WebODM Continuous Monitor
echo ========================================
echo.
echo This script will monitor WebODM and restart services if needed.
echo Press Ctrl+C to stop monitoring.
echo.

:monitor_loop
echo [%date% %time%] Checking WebODM status...

REM Check if Docker is running
wsl -d Ubuntu bash -c "sudo service docker status" >nul 2>&1
if errorlevel 1 (
    echo [%date% %time%] Docker service is down, starting...
    wsl -d Ubuntu bash -c "sudo service docker start"
    timeout /t 10 /nobreak >nul
)

REM Check container count
for /f "tokens=*" %%i in ('wsl -d Ubuntu bash -c "cd /home/<USER>/webodm && docker ps -q | wc -l"') do set RUNNING_COUNT=%%i

if %RUNNING_COUNT% LSS 5 (
    echo [%date% %time%] Only %RUNNING_COUNT%/5 containers running, restarting WebODM...
    wsl -d Ubuntu bash -c "cd /home/<USER>/webodm && ./webodm.sh start --detached" >nul 2>&1
    timeout /t 30 /nobreak >nul
) else (
    REM Check if webapp is responding
    powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:8000' -TimeoutSec 3 -UseBasicParsing; $status = $response.StatusCode } catch { $status = 'ERROR' }; if ($status -eq 200 -or $status -eq 302) { exit 0 } else { exit 1 }" >nul 2>&1
    if errorlevel 1 (
        echo [%date% %time%] WebODM not responding, restarting webapp...
        wsl -d Ubuntu bash -c "cd /home/<USER>/webodm && docker restart webapp" >nul 2>&1
        timeout /t 15 /nobreak >nul
    ) else (
        echo [%date% %time%] WebODM is healthy
    )
)

REM Wait 30 seconds before next check
timeout /t 30 /nobreak >nul
goto monitor_loop
