@echo off
setlocal enabledelayedexpansion

echo ========================================
echo WSL2 Docker Optimization Script
echo ========================================
echo.
echo This script will optimize WSL2 and Docker settings for better stability.
echo.

echo [1/5] Creating WSL2 configuration file...
(
echo [wsl2]
echo memory=8GB
echo processors=4
echo swap=2GB
echo localhostForwarding=true
) > "%USERPROFILE%\.wslconfig"

echo WSL2 configuration created at %USERPROFILE%\.wslconfig
echo.

echo [2/5] Optimizing Docker daemon settings in WSL2...
wsl -d Ubuntu bash -c "
sudo mkdir -p /etc/docker
sudo tee /etc/docker/daemon.json > /dev/null <<EOF
{
  \"log-driver\": \"json-file\",
  \"log-opts\": {
    \"max-size\": \"10m\",
    \"max-file\": \"3\"
  },
  \"storage-driver\": \"overlay2\",
  \"default-ulimits\": {
    \"nofile\": {
      \"Name\": \"nofile\",
      \"Hard\": 64000,
      \"Soft\": 64000
    }
  }
}
EOF
"

echo Docker daemon configuration updated.
echo.

echo [3/5] Setting up Docker service auto-start...
wsl -d Ubuntu bash -c "
echo '#!/bin/bash' | sudo tee /etc/init.d/docker-autostart > /dev/null
echo 'sudo service docker start' | sudo tee -a /etc/init.d/docker-autostart > /dev/null
sudo chmod +x /etc/init.d/docker-autostart
"

echo Docker auto-start configured.
echo.

echo [4/5] Creating WebODM service script...
wsl -d Ubuntu bash -c "
cat > /home/<USER>/start-webodm-service.sh << 'EOF'
#!/bin/bash
cd /home/<USER>/webodm

# Ensure Docker is running
sudo service docker start
sleep 5

# Start WebODM
export WO_HOST=0.0.0.0
./webodm.sh start --detached

# Wait for services to be ready
echo 'Waiting for WebODM to be ready...'
timeout 120 bash -c 'until curl -s http://localhost:8000 >/dev/null 2>&1; do sleep 2; done'
echo 'WebODM is ready!'
EOF

chmod +x /home/<USER>/start-webodm-service.sh
"

echo WebODM service script created.
echo.

echo [5/5] Testing optimized configuration...
echo Restarting WSL2 to apply changes...
wsl --shutdown
timeout /t 5 /nobreak >nul

echo Starting WSL2 with new configuration...
wsl -d Ubuntu bash -c "echo 'WSL2 restarted successfully'"

echo.
echo ========================================
echo Optimization Complete!
echo ========================================
echo.
echo Changes made:
echo 1. WSL2 memory limit set to 8GB
echo 2. Docker daemon optimized for stability
echo 3. Docker auto-start configured
echo 4. WebODM service script created
echo.
echo To start WebODM with optimized settings:
echo   wsl -d Ubuntu bash -c "/home/<USER>/start-webodm-service.sh"
echo.
echo Or use the existing: start-webodm-wsl2.bat
echo.
pause
