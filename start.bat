@echo off
setlocal enabledelayedexpansion

REM WebODM Windows Start Script
REM Equivalent to start.sh for Windows systems

set SCRIPT_DIR=%~dp0
cd /d "%SCRIPT_DIR%"

REM Display WebODM ASCII art
echo.
echo  _       __     __    ____  ____  __  ___
echo ^| ^|     / /__  / /_  / __ \/ __ \/  ^|/  /
echo ^| ^| /^| / / _ \/ __ \/ / / / / / / /^|_/ / 
echo ^| ^|/ ^|/ /  __/ /_/ / /_/ / /_/ / /  / /  
echo ^|__/^|__/\___/_.___/\____/_____/_/  /_/   
echo.

:almost_there
echo.
echo ====================
echo You're almost there!
echo ====================
goto :eof

REM Check Python version
python -c "import sys;ret = 1 if sys.version_info <= (3, 0) else 0;print('Checking python version... ' + ('3.x, good!' if ret == 0 else '2.x'));sys.exit(ret);" 2>nul
if errorlevel 1 (
    call :almost_there
    echo Your system is currently using Python 2.x. You need to install or configure your system to use Python 3.x.
    echo Check out http://docs.python-guide.org/en/latest/dev/virtualenvs/ for information on how to setup Python 3.x.
    echo.
    exit /b 1
)

REM Check GDAL version
python -c "import sys;import re;import subprocess;version = subprocess.Popen(['gdalinfo', '--version'], stdout=subprocess.PIPE).communicate()[0].decode().rstrip();ret = 0 if re.compile('^GDAL [2-9]\.[0-9]+').match(version) else 1; print('Checking GDAL version... ' + ('{}, excellent!'.format(version) if ret == 0 else version));sys.exit(ret);" 2>nul
if errorlevel 1 (
    call :almost_there
    echo Your system is currently using a version of GDAL that is too old, or GDAL is not installed.
    echo You need to install or configure your system to use GDAL 2.1 or higher.
    echo If you have installed multiple versions of GDAL, make sure the newer one takes priority in your PATH.
    echo.
    exit /b 1
)

REM Check for setup-devenv flag
set SETUP_DEVENV=false
if "%~1"=="--setup-devenv" set SETUP_DEVENV=true
if "%~2"=="--setup-devenv" set SETUP_DEVENV=true

if "%SETUP_DEVENV%"=="true" (
    echo Setup git modules...
    git submodule update --init
    
    echo Setup npm dependencies...
    npm install
    
    cd nodeodm\external\NodeODM
    npm install
    
    cd /d "%SCRIPT_DIR%"
    
    echo Setup pip requirements...
    pip install -r requirements.txt
    
    echo Build translations...
    python manage.py translate build --safe
    
    echo Setup webpack watch...
    start /b webpack --watch
)

echo Running migrations
python manage.py migrate

REM Setup default nodes if specified
if defined WO_DEFAULT_NODES (
    if %WO_DEFAULT_NODES% gtr 0 (
        set /a i=0
        :node_loop
        set /a i=!i!+1
        if !i! leq %WO_DEFAULT_NODES% (
            for /f "tokens=*" %%a in ('python manage.py getnodehostname webodm_node-odm_!i!') do set NODE_HOST=%%a
            python manage.py addnode !NODE_HOST! 3000 --label node-odm-!i!
            goto node_loop
        )
    )
)

REM Setup MicMac node if specified
if "%WO_CREATE_MICMAC_PNODE%"=="YES" (
    python manage.py addnode node-micmac-1 3000
)

REM Set default host and port if not defined
if not defined WO_HOST set WO_HOST=localhost
if not defined WO_PORT set WO_PORT=8000

REM Dump environment to .cronenv (Windows equivalent)
set > .cronenv

REM Determine protocol
set PROTO=http
if "%WO_SSL%"=="YES" set PROTO=https

REM Unlock all tasks
type app\scripts\unlock_all_tasks.py | python manage.py shell

REM Start worker scheduler
call worker.bat scheduler start

REM Function to check if WebODM is running (background process)
start /b cmd /c "call :congrats"

REM Check for different run modes
set NO_GUNICORN=false
if "%~1"=="--setup-devenv" set NO_GUNICORN=true
if "%~2"=="--setup-devenv" set NO_GUNICORN=true
if "%~1"=="--no-gunicorn" set NO_GUNICORN=true

if "%NO_GUNICORN%"=="true" (
    python manage.py runserver 0.0.0.0:8000
) else (
    REM Check if build/static exists
    if exist "\webodm" (
        if not exist "\webodm\build\static" (
            echo WARN: /webodm/build/static does not exist, CSS, JS and other files might not be available.
        )
    )
    
    echo Generating nginx configurations from templates...
    for %%f in (nginx\*.template) do (
        echo - %%~nf
        REM Use PowerShell to substitute environment variables (Windows equivalent of envsubst)
        powershell -Command "(Get-Content '%%f') -replace '\$WO_PORT','%WO_PORT%' -replace '\$WO_HOST','%WO_HOST%' | Set-Content '%%~dpnf'"
    )
    
    REM Check if we need to auto-generate SSL certs via letsencrypt
    if "%WO_SSL%"=="YES" (
        if not defined WO_SSL_KEY (
            echo Launching letsencrypt-autogen.sh
            call nginx\letsencrypt-autogen.bat
        )
    )
    
    REM Check if SSL key/certs are available
    set NGINX_CONF=nginx.conf
    if exist "nginx\ssl" (
        echo Using nginx SSL configuration
        set NGINX_CONF=nginx-ssl.conf
    )
    
    REM Set web concurrency if not defined
    if not defined WEB_CONCURRENCY (
        REM Get number of processors (Windows equivalent of nproc)
        set /a WEB_CONCURRENCY=%NUMBER_OF_PROCESSORS%*2+1
    )
    echo Web concurrency set to %WEB_CONCURRENCY%
    
    REM Start nginx and gunicorn
    start /b nginx -c "%CD%\nginx\%NGINX_CONF%"
    gunicorn webodm.wsgi --bind unix:/tmp/gunicorn.sock --timeout 300000 --max-requests 500 --workers %WEB_CONCURRENCY% --preload
)

goto :eof

:congrats
REM Wait 5 seconds
timeout /t 5 /nobreak >nul

echo.
echo Trying to establish communication...

REM Use PowerShell to check HTTP status (Windows equivalent of curl)
for /f "tokens=*" %%a in ('powershell -Command "try { $response = Invoke-WebRequest -Uri '%PROTO%://localhost:8000' -TimeoutSec 300 -UseBasicParsing; $response.StatusCode } catch { 'error' }"') do set STATUS=%%a

if "%STATUS%"=="200" (
    echo.
    echo Congratulations! └@(･◡･)@┐
    echo ==========================
    echo.
    echo If there are no errors, WebODM should be up and running!
) else (
    echo.
    echo Something doesn't look right! ¯\_(ツ)_/¯
    echo The server returned a status code of %STATUS% when we tried to reach it.
    echo ==========================
    echo.
    echo Check if WebODM is running, maybe we tried to reach it too soon.
)

echo.
echo Open a web browser and navigate to %PROTO%://%WO_HOST%:%WO_PORT%
echo.
goto :eof
