@echo off
setlocal enabledelayedexpansion

echo ========================================
echo WebODM WSL2 Alternative Setup
echo ========================================
echo.
echo This script sets up WebODM using Docker directly in WSL2
echo instead of Docker Desktop (which is currently broken)
echo.

echo [1/6] Checking WSL2 availability...
wsl --list --verbose >nul 2>&1
if errorlevel 1 (
    echo ERROR: WSL2 is not available
    echo Please install WSL2 first:
    echo   wsl --install
    goto :end
)

echo WSL2 is available
wsl --list --verbose
echo.

echo [2/6] Checking if Ubuntu is installed in WSL2...
wsl --list | find "Ubuntu" >nul
if errorlevel 1 (
    echo Installing Ubuntu in WSL2...
    wsl --install -d Ubuntu
    echo Please complete Ubuntu setup and run this script again
    goto :end
) else (
    echo Ubuntu is installed in WSL2
)
echo.

echo [3/6] Setting up Docker in WSL2 Ubuntu...
echo This will install Docker directly in the WSL2 Ubuntu environment
echo.

wsl -d Ubuntu bash -c "
echo 'Setting up Docker in WSL2...'
sudo apt update
sudo apt install -y docker.io docker-compose
sudo usermod -aG docker \$USER
sudo service docker start
echo 'Docker installed in WSL2'
"

if errorlevel 1 (
    echo Failed to install Docker in WSL2
    goto :end
)
echo.

echo [4/6] Copying WebODM files to WSL2...
set WSL_HOME=/home/<USER>
wsl -d Ubuntu bash -c "mkdir -p %WSL_HOME%/webodm"

REM Copy WebODM files to WSL2
echo Copying files...
wsl -d Ubuntu bash -c "cp -r /mnt/c/Users/<USER>/Downloads/WebODM-2.9.0/WebODM-2.9.0/* %WSL_HOME%/webodm/"
echo.

echo [5/6] Creating WSL2 WebODM startup script...
wsl -d Ubuntu bash -c "cat > %WSL_HOME%/webodm/start-webodm-wsl2.sh << 'EOF'
#!/bin/bash
cd /home/<USER>/webodm

# Start Docker service if not running
sudo service docker start

# Set environment variables
export WO_HOST=0.0.0.0
export WO_PORT=8000
export DOCKER_DEFAULT_PLATFORM=linux/amd64

echo 'Starting WebODM in WSL2...'
./webodm.sh start

echo 'WebODM is running!'
echo 'Access it at: http://localhost:8000'
EOF"

wsl -d Ubuntu bash -c "chmod +x %WSL_HOME%/webodm/start-webodm-wsl2.sh"
echo.

echo [6/6] Testing WSL2 Docker setup...
wsl -d Ubuntu bash -c "
sudo service docker start
docker run --rm hello-world
"

if errorlevel 1 (
    echo WSL2 Docker test failed
    goto :troubleshoot_wsl2
) else (
    echo WSL2 Docker is working!
)
echo.

echo ========================================
echo WSL2 SETUP COMPLETE
echo ========================================
echo.
echo To start WebODM using WSL2:
echo.
echo Option 1 - Use the WSL2 script:
echo   wsl -d Ubuntu bash -c "/home/<USER>/webodm/start-webodm-wsl2.sh"
echo.
echo Option 2 - Manual WSL2 commands:
echo   wsl -d Ubuntu
echo   cd /home/<USER>/webodm
echo   sudo service docker start
echo   ./webodm.sh start
echo.
echo WebODM will be accessible at: http://localhost:8000
echo.
goto :end

:troubleshoot_wsl2
echo.
echo ========================================
echo WSL2 TROUBLESHOOTING
echo ========================================
echo.
echo WSL2 Docker setup failed. Try these steps:
echo.
echo 1. Restart WSL2:
echo    wsl --shutdown
echo    wsl -d Ubuntu
echo.
echo 2. Manually install Docker in WSL2:
echo    wsl -d Ubuntu
echo    sudo apt update
echo    sudo apt install docker.io docker-compose
echo    sudo service docker start
echo.
echo 3. Check Docker in WSL2:
echo    wsl -d Ubuntu
echo    sudo docker run hello-world
echo.

:end
echo.
pause
