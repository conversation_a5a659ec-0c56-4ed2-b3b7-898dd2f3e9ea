{"name": "Task Notification", "webodmMinVersion": "0.6.2", "description": "Get notified when a task has finished processing, has been removed or has failed", "version": "0.1.0", "author": "<PERSON>", "email": "<EMAIL>", "repository": "https://github.com/OpenDroneMap/WebODM", "tags": ["notification", "email", "smtp"], "homepage": "https://github.com/OpenDroneMap/WebODM", "experimental": false, "deprecated": false}