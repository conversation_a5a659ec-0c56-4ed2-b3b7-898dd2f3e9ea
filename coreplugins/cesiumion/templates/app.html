{% extends "app/plugins/templates/base.html" %}
{% block content %}
<style>
	.alert {
		position: absolute;
		z-index: 100000;
		width: 79vw;
		bottom: 0.5em;
		right: 1em;
		width: 18em;
	}
	#navbar-top.cesium-navbar {
		margin: -15px;
		margin-top: -10px;
		margin-bottom: 12px;
		position: relative;
	}
	#navbar-top.cesium-navbar > .navbar-text {
		margin: 0;
		left: 15px;
		top: 50%;
		transform: translateY(-50%);
	}
	#navbar-top.cesium-navbar .description {
		font-size: 10px;
		margin-left: 28px
	}
</style>
<nav id="navbar-top" class="navbar-default cesium-navbar">
<h4 class="navbar-text">
	<i class="fa fa-cesium fa-fw"></i> <strong>Cesium Ion</strong>
	<p class="description">
		Use Cesium Ion's simple workflow to create 3D maps of your geospatial
		data for visualization, analysis, and sharing
	</p>
</h4>
</div>
{% if not form.token.value %}
<h5>
	<strong>Instructions</strong>
</h5>
<ol>
	<li>
		Generate a token at
		<a href="https://cesium.com/ion/tokens" target="_blank"> cesium.com/ion/tokens </a>
		with <b>all permissions:</b>
		<i>assets:list, assets:read, assets:write, geocode.</i>
	</li>
	<li>Copy and paste the token into the form below.</li>
</ol>
{% else %}
<p><b>You are all set!</b> To share a task, select it from the <a href="/dashboard/">dashboard</a> and press the <b>Tile in Cesium ion</b> button.</p>
<p>
	<a class="btn btn-sm btn-primary" href="/dashboard"><i class="fa fa-external-link"></i> Go To Dashboard</a>
	<a class="btn btn-sm btn-default" href="https://cesium.com/ion" target="_blank"><i class="fa fa-cesium"></i> Open Cesium Ion</a>
</p>
{% endif %}
<form action="" method="post" class="oam-form oam-token-form">
	<h5><b>Token Settings</b></h5>
	{% csrf_token %}
	{% include "app/plugins/templates/form.html" %}
	<button type="submit" class="btn btn-primary"><i class="fa fa-save fa-fw"></i> Set Token</i></button>
</form>
{% endblock %}
