{% extends "app/plugins/templates/base.html" %}

{% block content %}
<script>
PluginsAPI.App.ready([
        'lightning/build/app.js',
        'lightning/build/app.css'
	], function(args, App){
    
    $("[data-lightning-panel]").each(function(){
        let props = $(this).data();
        delete(props.lightningPanel);
        window.ReactDOM.render(window.React.createElement(App, props), $(this).get(0));
    });
});
</script>

<div class="row">
    <div class="col-md-12">
        <div data-lightning-panel data-api-key="{{ api_key }}"></div>
    </div>
</div>
{% endblock %}