@font-face {
  font-family: 'oamfont';
  src:  url('fonts/oamfont.eot?7ho5xe');
  src:  url('fonts/oamfont.eot?7ho5xe#iefix') format('embedded-opentype'),
    url('fonts/oamfont.ttf?7ho5xe') format('truetype'),
    url('fonts/oamfont.woff?7ho5xe') format('woff'),
    url('fonts/oamfont.svg?7ho5xe#oamfont') format('svg');
  font-weight: normal;
  font-style: normal;
}

.oam-icon {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'oamfont' !important;
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.oam-icon:before {
  content: "\e900";
}

.oam-form{
  margin-top: 16px;
}

.oam-token-form label{
    display: none;
}