@font-face {
    font-family: 'ddbfont';
    src: url('fonts/ddb.eot?16744445');
    src: url('fonts/ddb.eot?16744445#iefix') format('embedded-opentype'),
         url('fonts/ddb.woff2?16744445') format('woff2'),
         url('fonts/ddb.woff?16744445') format('woff'),
         url('fonts/ddb.ttf?16744445') format('truetype'),
         url('fonts/ddb.svg?16744445#ddb') format('svg');
    font-weight: normal;
    font-style: normal;
  }

  .ddb-icon:before {
    font-family: "ddbfont";
    font-style: normal;
    font-weight: normal;
    speak: never;
  
    display: inline-block;
    text-decoration: inherit;
    width: 1em;
    margin-right: .2em;
    text-align: center;
    /* opacity: .8; */
  
    /* For safety - reset parent styles, that can break glyph codes*/
    font-variant: normal;
    text-transform: none;
  
    /* fix buttons height, for twitter bootstrap */
    line-height: 1em;
  
    /* Font smoothing. That was taken from TWBS */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  
    /* Uncomment for 3D effect */
     text-shadow: 1px 1px 1px rgba(127, 127, 127, 0.3); 

     content: '\e803';
  }
 
  