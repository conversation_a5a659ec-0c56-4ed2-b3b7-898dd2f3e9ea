{% extends "app/plugins/templates/base.html" %}
{% load i18n %}

{% block content %}
<script src="./Chart.min.js"></script>
<h2>{% trans 'Diagnostic Information' %}</h2>

<div class="row text-center">
    <div class="col-md-4 col-sm-12">
        <h4>{% trans 'Storage Space' %}</h4>
        <div style="width: 80%; margin-left: 10%;">
            <canvas id="diskChart" width="200" height="200" style="margin-bottom: 12px;"></canvas>
        </div>
        <p><b>{% trans 'Free' context 'Megabytes of storage space' %}:</b> {{ free_disk_space|filesizeformat }} |
            <b>{% trans 'Used' context 'Megabytes of storage space' %}:</b> {{ used_disk_space|filesizeformat }} |
            <b>{% trans 'Total' context 'Megabytes of storage space' %}:</b> {{ total_disk_space|filesizeformat }}</p>
    </div>
    <div class="col-md-4 col-sm-12">
        {% if total_memory %}
        <h4>{% trans 'Memory' context 'Computer memory (RAM)' %}</h4>
        <div style="width: 80%; margin-left: 10%;">
            <canvas id="memoryChart" width="200" height="200" style="margin-bottom: 12px;"></canvas>
        </div>
        <p><b>{% trans 'Free' context 'Megabytes of memory space' %}:</b> {{ free_memory|filesizeformat }} |
            <b>{% trans 'Used' context 'Megabytes of memory space' %}:</b> {{ used_memory|filesizeformat }} |
            <b>{% trans 'Total' context 'Megabytes of memory space'%}:</b> {{ total_memory|filesizeformat }}</p>
        {% endif %}
    </div>
</div>

<hr/>

<div style="margin-top: 20px;"><strong>{% trans 'Note!' %}</strong> {% blocktrans with win_hyperv_link="<a href='https://docs.docker.com/desktop/settings/windows/#resources'>Windows (Hyper-V)</a>" win_wsl2_link="<a href='https://learn.microsoft.com/en-us/windows/wsl/wsl-config#configuration-setting-for-wslconfig'>Windows (WSL2)</a>" mac_link="<a href='https://docs.docker.com/desktop/settings/mac/#resources'>MacOS</a>" %}These values might be relative to the virtualization environment in which the application is running, not necessarily the values of the your machine. See instructions for {{ win_hyperv_link }}, {{ win_wsl2_link }}, and {{ mac_link }} for changing these values in a Docker setup.{% endblocktrans %}</div>

<script>
(function(){
    var ctx = document.getElementById('diskChart').getContext('2d');
    var labels = {
        "{% trans 'Used' context 'Megabytes of storage space' %}": '{{ used_disk_space|filesizeformat }}',
        "{% trans 'Free' context 'Megabytes of storage space' %}": '{{ free_disk_space|filesizeformat }}'
    };
    var chart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ["{% trans 'Used' context 'Megabytes of storage space' %}", "{% trans 'Free' context 'Megabytes of storage space' %}"],
            datasets: [{
                label: "{% trans 'Disk Space' %}",
                backgroundColor:[
                    "rgb(255, 99, 132)",
                    "rgb(54, 162, 235)"
                ],
                data: [ {{ used_disk_space }}, {{ free_disk_space }} ],
            }]
        },
        options: {
            legend:{
                reverse: true
            },
            tooltips: {
                callbacks: {
                    label: function(tooltipItem, data) {
                        return labels[data.labels[tooltipItem.index]];
                    }
                }
            }
        }
    });
})();

{% if total_memory %}
(function(){
    var ctx = document.getElementById('memoryChart').getContext('2d');
    var labels = {
        "{% trans 'Used' context 'Megabytes of memory space' %}": '{{ used_memory|filesizeformat }}',
        "{% trans 'Free' context 'Megabytes of memory space' %}": '{{ free_memory|filesizeformat }}'
    };
    var chart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ["{% trans 'Used' context 'Megabytes of memory space' %}", "{% trans 'Free' context 'Megabytes of memory space' %}"],
            datasets: [{
                label: "{% trans 'Disk Space' %}",
                backgroundColor:[
                    "rgb(255, 99, 132)",
                    "rgb(54, 162, 235)"
                ],
                data: [ {{ used_memory }}, {{ free_memory }} ],
            }]
        },
        options: {
            legend:{
                reverse: true
            },
            tooltips: {
                callbacks: {
                    label: function(tooltipItem, data) {
                        return labels[data.labels[tooltipItem.index]];
                    }
                }
            }
        }
    });
})();
{% endif %}
</script>
{% endblock %}
