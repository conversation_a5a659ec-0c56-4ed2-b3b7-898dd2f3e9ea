worker_processes 1;

# Change this if running outside docker!
user root root;
pid /tmp/nginx.pid;
error_log /dev/null;

events {
  worker_connections 1024; # increase if you have lots of clients
  accept_mutex off; # set to 'on' if nginx worker_processes > 1
  use epoll;
}

http {
  include /etc/nginx/mime.types;

  # fallback in case we can't determine a type
  default_type application/octet-stream;
  access_log off;
  sendfile on;

  upstream app_server {
    # fail_timeout=0 means we always retry an upstream even if it failed
    # to return a good HTTP response

    # for UNIX domain socket setups
    server unix:/tmp/gunicorn.sock fail_timeout=0;
  }

  server {
    listen 8000 deferred;
    listen [::]:8000 deferred;
    client_max_body_size 0;

    server_name $WO_HOST;

    keepalive_timeout 5;

    proxy_connect_timeout 60s;
    proxy_read_timeout 300000s;

    # path for static files
    location /static {
      root /webodm/build;
    }

    # path for certain media files that don't need permissions enforced
    location /media/CACHE {
      root /webodm/app;
    }
    location /media/settings {
      autoindex on;
      root /webodm/app;
    }

    location / {
      proxy_http_version 1.1;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;

      # enable this if and only if you use HTTPS
      # proxy_set_header X-Forwarded-Proto https;
      proxy_set_header Host $http_host;

      # we don't want nginx trying to do something clever with
      # redirects, we set the Host: header above already.
      proxy_redirect off;

      proxy_pass http://app_server;
    }

    # Same as above, but adds proxy_buffering off
    location ~* (download/.*\.zip|backup)$ {
      proxy_http_version 1.1;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;

      # enable this if and only if you use HTTPS
      # proxy_set_header X-Forwarded-Proto https;
      proxy_set_header Host $http_host;

      # we don't want nginx trying to do something clever with
      # redirects, we set the Host: header above already.
      proxy_redirect off;

      # For streaming requests
      proxy_buffering off;

      proxy_pass http://app_server;
    }
  }
}
