name: Issue Triage
on:
  issues:
    types:
      - opened
jobs:
  issue_triage:
    runs-on: ubuntu-latest
    permissions:
      issues: write
    steps:
      - uses: pierotofy/issuewhiz@v1
        with:
          ghToken: ${{ secrets.GITHUB_TOKEN }}
          openAI: ${{ secrets.OPENAI_TOKEN }}
          model: gpt-4o-2024-08-06
          filter: |
            - "#"
          variables: |
            - Q: "A question about using a software or seeking guidance on doing something?"
            - B: "Reporting an issue or a software bug?"
            - P: "Describes an issue with processing a set of images or a particular dataset?"
            - D: "Contains a link to a dataset or images?"
            - E: "Contains a suggestion for an improvement or a feature request?"
            - SC: "Describes an issue related to compiling or building source code?"
          logic: |
            - 'Q and (not B) and (not P) and (not E) and (not SC) and not (title_lowercase ~= ".*bug: .+")': [comment: "Could we move this conversation over to the forum at https://community.opendronemap.org? :pray: The forum is the right place to ask questions (we try to keep the GitHub issue tracker for feature requests and bugs only). Thank you! :+1:", close: true, stop: true]
            - "B and (not P) and (not E) and (not SC)": [label: "software fault", stop: true]
            - "P and D": [label: "possible software fault", stop: true]
            - "P and (not D) and (not SC) and (not E)": [comment: "Thanks for the report, but it looks like you didn't include a copy of your dataset for us to reproduce this issue? Please make sure to follow our [issue guidelines](https://github.com/OpenDroneMap/WebODM/blob/master/ISSUE_TEMPLATE.md) :pray: ", close: true, stop: true]
            - "E": [label: enhancement, stop: true]
            - "SC": [label: "possible software fault"]

          signature: "p.s. I'm just an automated script, not a human being."
