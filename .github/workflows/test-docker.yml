name: Test Docker

on:
  pull_request:

jobs:
  docker:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
      with:
        submodules: 'recursive'
      name: Checkout
    - name: Set Swap Space
      uses: pierotofy/set-swap-space@master
      with:
        swap-size-gb: 12
    - name: Build and Test
      run: |
        docker compose -f docker-compose.yml -f docker-compose.build.yml build --build-arg TEST_BUILD=ON
        docker compose -f docker-compose.yml -f docker-compose.build.yml up -d
        sleep 20
        docker compose exec -T webapp /webodm/webodm.sh test
