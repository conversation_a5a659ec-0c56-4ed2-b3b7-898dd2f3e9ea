name: Build and Publish Docker Image

on:
  push:
    branches:
    - master
    tags:
    - v*

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout
      uses: actions/checkout@v3
      with:
        submodules: 'recursive'
    - name: Set up QEMU
      uses: docker/setup-qemu-action@v3
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    - name: Login to DockerHub
      uses: docker/login-action@v1 
      with:
        username: ${{ secrets.DOCKERHUB_USERNAME }}
        password: ${{ secrets.DOCKERHUB_TOKEN }}
    - name: Docker meta
      id: docker_meta
      uses: crazy-max/ghaction-docker-meta@v1
      with:
        images: opendronemap/webodm_webapp
        tag-semver: |
          {{version}}
    - name: Build and push Docker image
      id: docker_build
      uses: docker/build-push-action@v2
      with:
        file: ./Dockerfile
        platforms: linux/amd64,linux/arm64
        push: true
        no-cache: true
        tags: |
          ${{ steps.docker_meta.outputs.tags }}
          opendronemap/webodm_webapp:latest
    - name: Image digest
      run: echo ${{ steps.docker_build.outputs.digest }}
