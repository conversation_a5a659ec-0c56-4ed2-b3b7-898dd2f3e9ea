# name: Build and Publish Docs

# on:
#   push:
#     branches:
#     - master

# jobs:
#   build:
#     runs-on: ubuntu-latest
#     env:
#       ruby-version: 2.7

#     steps:
#     - uses: actions/checkout@v3
#     - name: Set up Ruby
#       uses: ruby/setup-ruby@v1
#       with:
#         ruby-version: ${{ env.ruby-version }}
#     - name: bundle install
#       working-directory: ./slate
#       run: bundle install
#     - name: bundle exec
#       working-directory: ./slate
#       run: bundle exec middleman build
#     - name: Deploy
#       uses: peaceiris/actions-gh-pages@v3
#       with:
#         github_token: ${{ secrets.GITHUB_TOKEN }}
#         publish_dir: ./slate/build
#         keep_files: true
