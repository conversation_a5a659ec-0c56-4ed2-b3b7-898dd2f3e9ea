#!/bin/bash
cd /home/<USER>/webodm

echo 'Starting optimized WebODM...'

# Ensure Docker is running
sudo service docker start
sleep 5

# Clean up any stopped containers
docker container prune -f

# Start WebODM with optimized settings
export WO_HOST=0.0.0.0
export DOCKER_DEFAULT_PLATFORM=linux/amd64
./webodm.sh start --detached

# Wait for services to be ready
echo 'Waiting for WebODM to be ready...'
for i in {1..40}; do
    if curl -s http://localhost:8000 >/dev/null 2>&1; then
        echo 'WebODM webapp is responding!'
        break
    fi
    echo "Attempt $i/40: Waiting for webapp..."
    sleep 3
done

echo 'Testing NodeODM connectivity...'
for i in {1..15}; do
    if docker exec webapp curl -s http://webodm_node-odm_1:3000/info >/dev/null 2>&1; then
        echo 'NodeODM connectivity confirmed!'
        break
    fi
    echo "Attempt $i/15: Waiting for NodeODM..."
    sleep 2
done

echo 'WebODM is ready and optimized!'
echo 'Access it at: http://localhost:8000'
