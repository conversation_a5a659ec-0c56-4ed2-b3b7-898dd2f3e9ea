@echo off
setlocal enabledelayedexpansion

echo ========================================
echo Fix Image Processing Crashes
echo ========================================
echo.
echo This script applies fixes for crashes that occur during
echo image and GCP file processing in WebODM.
echo.

echo [1/6] Stopping WebODM to apply fixes...
wsl -d Ubuntu bash -c "cd /home/<USER>/webodm && ./webodm.sh down"

echo.
echo [2/6] Increasing WSL2 memory allocation...
powershell -Command "Set-Content -Path '$env:USERPROFILE\.wslconfig' -Value @('[wsl2]', 'memory=12GB', 'processors=6', 'swap=4GB', 'localhostForwarding=true')"

echo WSL2 memory increased to 12GB with 4GB swap

echo.
echo [3/6] Creating optimized WebODM configuration...
wsl -d Ubuntu bash -c "cat > /home/<USER>/webodm/.env << 'EOF'
WO_HOST=0.0.0.0
WO_PORT=8000
WO_MEDIA_DIR=appmedia
WO_DB_DIR=dbdata
WO_SSL=NO
WO_SSL_KEY=
WO_SSL_CERT=
WO_SSL_INSECURE_PORT_REDIRECT=80
WO_DEBUG=NO
WO_DEV=NO
WO_BROKER=redis://broker
WO_DEFAULT_NODES=1
WO_SETTINGS=
WO_WORKER_MEMORY=4096
WO_WORKER_CPUS=2
EOF"

echo.
echo [4/6] Creating memory-optimized startup script...
wsl -d Ubuntu bash -c "cat > /home/<USER>/start-webodm-memory-optimized.sh << 'EOF'
#!/bin/bash
cd /home/<USER>/webodm

echo 'Starting memory-optimized WebODM...'

# Ensure Docker is running
sudo service docker start
sleep 5

# Set memory limits and optimizations
export WO_HOST=0.0.0.0
export DOCKER_DEFAULT_PLATFORM=linux/amd64
export WO_WORKER_MEMORY=4096
export WO_WORKER_CPUS=2

# Clean up before starting
docker container prune -f
docker system prune -f

# Start with memory constraints
./webodm.sh start --detached --worker-memory 4096 --worker-cpus 2

echo 'Waiting for services to initialize...'
sleep 30

# Verify all containers are running
docker ps --format \"table {{.Names}}\t{{.Status}}\"

echo 'Testing connectivity...'
timeout 60 bash -c \"until curl -s http://localhost:8000 >/dev/null 2>&1; do echo 'Waiting for webapp...'; sleep 3; done\"

echo 'WebODM memory-optimized startup complete!'
echo 'Access at: http://localhost:8000'
echo ''
echo 'IMPORTANT: Process images in smaller batches to avoid crashes'
echo 'Recommended: Upload 10-20 images at a time instead of large batches'
EOF"

wsl -d Ubuntu bash -c "chmod +x /home/<USER>/start-webodm-memory-optimized.sh"

echo.
echo [5/6] Restarting WSL2 with new memory settings...
wsl --shutdown
timeout /t 10 /nobreak >nul

echo Starting WSL2 with increased memory...
wsl -d Ubuntu bash -c "echo 'WSL2 restarted with 12GB memory' && free -h"

echo.
echo [6/6] Starting WebODM with memory optimizations...
wsl -d Ubuntu bash -c "/home/<USER>/start-webodm-memory-optimized.sh"

echo.
echo ========================================
echo Memory Optimization Complete!
echo ========================================
echo.
echo Changes applied:
echo - WSL2 memory increased to 12GB
echo - WebODM worker memory limited to 4GB
echo - WebODM worker CPUs limited to 2
echo - Added swap space (4GB)
echo - Optimized startup process
echo.
echo RECOMMENDATIONS TO PREVENT CRASHES:
echo.
echo 1. Upload images in smaller batches (10-20 at a time)
echo 2. Use compressed/resized images when possible
echo 3. Monitor memory usage during processing
echo 4. If crashes still occur, reduce batch size further
echo.
echo To start WebODM in the future:
echo   wsl -d Ubuntu bash -c "/home/<USER>/start-webodm-memory-optimized.sh"
echo.
echo Or use: start-webodm-optimized.bat
echo.
pause
