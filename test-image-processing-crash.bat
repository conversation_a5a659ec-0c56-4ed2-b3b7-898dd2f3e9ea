@echo off
setlocal enabledelayedexpansion

echo ========================================
echo WebODM Image Processing Crash Test
echo ========================================
echo.
echo This script monitors WebODM specifically during image/GCP processing
echo to identify if that's when crashes occur.
echo.

set LOGFILE=image-processing-test-%date:~-4,4%%date:~-10,2%%date:~-7,2%-%time:~0,2%%time:~3,2%%time:~6,2%.log
set LOGFILE=%LOGFILE: =0%

echo Starting image processing monitoring at %date% %time% > %LOGFILE%
echo.

echo [1/5] Checking baseline system state...
echo ========================================

echo Baseline container status:
wsl -d Ubuntu bash -c "cd /home/<USER>/webodm && docker ps --format 'table {{.Names}}\t{{.Status}}\t{{.Ports}}'"

echo.
echo Baseline resource usage:
wsl -d Ubuntu bash -c "cd /home/<USER>/webodm && docker stats --no-stream --format 'table {{.Name}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}'"

echo.
echo Baseline memory:
wsl -d Ubuntu bash -c "free -h"

echo.
echo [2/5] Testing processing node BEFORE image upload...
echo ===================================================

powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:8000/processingnode/1/' -TimeoutSec 5 -UseBasicParsing; Write-Host 'Processing Node (before): OK (' $response.StatusCode ')' -ForegroundColor Green } catch { Write-Host 'Processing Node (before): FAILED -' $_.Exception.Message -ForegroundColor Red }"

echo.
echo [3/5] Simulating image processing load...
echo =========================================

echo Simulating memory-intensive operations that happen during image processing...

REM Test 1: Simulate image validation load
echo Testing image validation simulation...
wsl -d Ubuntu bash -c "cd /home/<USER>/webodm && timeout 30 docker exec webapp python -c '
import time
import os
print(\"Simulating image validation...\")
# Simulate memory usage similar to image processing
data = []
for i in range(100):
    data.append(b\"x\" * 1024 * 1024)  # 1MB chunks
    if i % 10 == 0:
        print(f\"Processed {i} simulated images...\")
    time.sleep(0.1)
print(\"Image validation simulation complete\")
' 2>&1 | tee -a %LOGFILE%"

echo.
echo Resource usage during simulation:
wsl -d Ubuntu bash -c "cd /home/<USER>/webodm && docker stats --no-stream --format 'table {{.Name}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}'"

echo.
echo [4/5] Testing processing node DURING load...
echo ============================================

powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:8000/processingnode/1/' -TimeoutSec 5 -UseBasicParsing; Write-Host 'Processing Node (during load): OK (' $response.StatusCode ')' -ForegroundColor Green } catch { Write-Host 'Processing Node (during load): FAILED -' $_.Exception.Message -ForegroundColor Red }"

echo.
echo Testing NodeODM connectivity during load...
wsl -d Ubuntu bash -c "cd /home/<USER>/webodm && timeout 10 docker exec webapp curl -s --connect-timeout 5 http://webodm_node-odm_1:3000/info >/dev/null 2>&1 && echo 'NodeODM (during load): OK' || echo 'NodeODM (during load): FAILED'"

echo.
echo [5/5] Testing processing node AFTER load...
echo ===========================================

echo Waiting 10 seconds for system to stabilize...
timeout /t 10 /nobreak >nul

powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:8000/processingnode/1/' -TimeoutSec 5 -UseBasicParsing; Write-Host 'Processing Node (after): OK (' $response.StatusCode ')' -ForegroundColor Green } catch { Write-Host 'Processing Node (after): FAILED -' $_.Exception.Message -ForegroundColor Red }"

echo.
echo Final resource usage:
wsl -d Ubuntu bash -c "cd /home/<USER>/webodm && docker stats --no-stream --format 'table {{.Name}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}'"

echo.
echo Final container status:
wsl -d Ubuntu bash -c "cd /home/<USER>/webodm && docker ps --format 'table {{.Names}}\t{{.Status}}'"

echo.
echo ========================================
echo Test Results Summary
echo ========================================
echo.
echo Check the log file: %LOGFILE%
echo.
echo If the processing node fails during or after the load test,
echo this confirms that image/GCP processing is triggering the crashes.
echo.
echo Recommended solutions if crash confirmed:
echo 1. Increase WSL2 memory allocation
echo 2. Reduce WebODM worker concurrency
echo 3. Add swap space
echo 4. Process smaller batches of images
echo.
pause
