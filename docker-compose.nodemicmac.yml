# Chaining this file to the main docker-compose file adds
# a default processing node instance. This is best for users
# who are just getting started with WebODM.

version: '2.1'
services:
  webapp:
    depends_on:
      - node-micmac-1
    environment:
      - WO_CREATE_MICMAC_PNODE=YES
  node-micmac-1:
    image: opendronemap/nodemicmac
    container_name: node-micmac-1
    expose:
      - "3000"
    restart: unless-stopped
    oom_score_adj: 500
