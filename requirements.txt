amqp==2.5.2
appdirs==1.4.0
APScheduler==3.2.0
billiard==3.6.3.0
celery==4.4.0
coreapi>=2.3.3
Django==2.2.27
django-appconf==1.0.2
django-codemirror2==0.2
django-colorfield==0.1.15
django-cors-headers==3.0.2
django-filter==2.4.0
django-guardian==1.4.9
django-imagekit==4.0.1
django-redis==4.12.1
django-webpack-loader==0.6.0
djangorestframework==3.13.1
djangorestframework-jwt==1.9.0
djangorestframework-guardian==0.3.0
drf-nested-routers==0.11.1
funcsigs==1.0.2
futures==3.1.1
gunicorn==23.0.0
geodeep==0.9.8
itypes==1.1.0
kombu==4.6.7
Markdown==3.3.4
olefile==0.44
openapi-codec==1.1.7
packaging==16.8
piexif==1.0.13
pilkit==2.0
Pillow==8.3.2
pip-autoremove==0.9.0
psycopg2-binary==2.8.6
PyJWT==1.5.3
pydantic==1.10.8
pyodm==1.5.11
pyparsing==2.4.7
pytz==2020.1
rcssmin==1.0.6
redis==3.2.0
requests-toolbelt==0.9.1
requests>=2.21.0
rfc3987==1.3.7
rjsmin==1.0.12
simplejson==3.10.0
six==1.11.0
strict-rfc3339==0.7
tzlocal==1.3
uritemplate==3.0.0
vine==1.3.0
webcolors==1.5
rio-tiler==2.1.2
rio-color==1.0.4
rio-cogeo==2.3.1
rasterio==1.2.9 ; sys_platform == 'linux' or sys_platform == 'darwin'
https://github.com/OpenDroneMap/WebODM/releases/download/v1.9.7/rasterio-1.2.10-cp39-cp39-win_amd64.whl ; sys_platform == "win32"
https://github.com/OpenDroneMap/WebODM/releases/download/v1.9.7/GDAL-3.3.3-cp39-cp39-win_amd64.whl ; sys_platform == "win32"
Shapely==1.8.0 ; sys_platform == "win32"
eventlet==0.32.0 ; sys_platform == "win32"
pyopenssl==24.0.0 ; sys_platform == "win32"
numpy==1.26.2
scipy==1.11.3
drf-yasg==1.20.0
zipstream-ng==1.8.0
