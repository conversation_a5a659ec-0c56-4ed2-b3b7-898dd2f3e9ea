@echo off
setlocal enabledelayedexpansion

REM WebODM Windows Batch Script
REM Equivalent to webodm.sh for Windows systems

set SCRIPT_DIR=%~dp0
cd /d "%SCRIPT_DIR%"

REM Platform detection
set PLATFORM=Windows

REM Set Windows-specific Docker Compose environment
set COMPOSE_CONVERT_WINDOWS_PATHS=1

REM Force x86_64/AMD64 platform to avoid "exec format error" on Windows
set DOCKER_DEFAULT_PLATFORM=linux/amd64

REM Initialize variables
set DEV_MODE=false
set GPU=false
set DETACHED=false
set LOAD_MICMAC_NODE=false
set IPV6=false

REM Load default values from .env file
if exist ".env" (
    for /f "usebackq tokens=1,2 delims==" %%a in (".env") do (
        if "%%a"=="WO_PORT" set DEFAULT_PORT=%%b
        if "%%a"=="WO_HOST" set DEFAULT_HOST=%%b
        if "%%a"=="WO_MEDIA_DIR" set DEFAULT_MEDIA_DIR=%%b
        if "%%a"=="WO_DB_DIR" set DEFAULT_DB_DIR=%%b
        if "%%a"=="WO_SSL" set DEFAULT_SSL=%%b
        if "%%a"=="WO_SSL_INSECURE_PORT_REDIRECT" set DEFAULT_SSL_INSECURE_PORT_REDIRECT=%%b
        if "%%a"=="WO_BROKER" set DEFAULT_BROKER=%%b
        if "%%a"=="WO_DEFAULT_NODES" set DEFAULT_NODES=%%b
    )
)

REM Set environment variables from .env if not already set
if not defined WO_PORT set WO_PORT=%DEFAULT_PORT%
if not defined WO_HOST set WO_HOST=%DEFAULT_HOST%
if not defined WO_MEDIA_DIR set WO_MEDIA_DIR=%DEFAULT_MEDIA_DIR%
if not defined WO_DB_DIR set WO_DB_DIR=%DEFAULT_DB_DIR%
if not defined WO_SSL set WO_SSL=%DEFAULT_SSL%
if not defined WO_SSL_INSECURE_PORT_REDIRECT set WO_SSL_INSECURE_PORT_REDIRECT=%DEFAULT_SSL_INSECURE_PORT_REDIRECT%
if not defined WO_BROKER set WO_BROKER=%DEFAULT_BROKER%
if not defined WO_DEFAULT_NODES set WO_DEFAULT_NODES=%DEFAULT_NODES%

REM Parse command line arguments
:parse_args
if "%~1"=="" goto end_parse_args

if "%~1"=="--port" (
    set WO_PORT=%~2
    shift
    shift
    goto parse_args
)
if "%~1"=="--hostname" (
    set WO_HOST=%~2
    shift
    shift
    goto parse_args
)
if "%~1"=="--media-dir" (
    set WO_MEDIA_DIR=%~2
    shift
    shift
    goto parse_args
)
if "%~1"=="--db-dir" (
    set WO_DB_DIR=%~2
    shift
    shift
    goto parse_args
)
if "%~1"=="--ssl" (
    set WO_SSL=YES
    shift
    goto parse_args
)
if "%~1"=="--ssl-key" (
    set WO_SSL_KEY=%~2
    shift
    shift
    goto parse_args
)
if "%~1"=="--ssl-cert" (
    set WO_SSL_CERT=%~2
    shift
    shift
    goto parse_args
)
if "%~1"=="--ssl-insecure-port-redirect" (
    set WO_SSL_INSECURE_PORT_REDIRECT=%~2
    shift
    shift
    goto parse_args
)
if "%~1"=="--debug" (
    set WO_DEBUG=YES
    shift
    goto parse_args
)
if "%~1"=="--dev-watch-plugins" (
    set WO_DEV_WATCH_PLUGINS=YES
    shift
    goto parse_args
)
if "%~1"=="--dev" (
    set WO_DEBUG=YES
    set WO_DEV=YES
    set DEV_MODE=true
    shift
    goto parse_args
)
if "%~1"=="--gpu" (
    set GPU=true
    shift
    goto parse_args
)
if "%~1"=="--broker" (
    set WO_BROKER=%~2
    shift
    shift
    goto parse_args
)
if "%~1"=="--no-default-node" (
    echo ATTENTION: --no-default-node is deprecated. Use --default-nodes instead.
    set WO_DEFAULT_NODES=0
    shift
    goto parse_args
)
if "%~1"=="--with-micmac" (
    set LOAD_MICMAC_NODE=true
    shift
    goto parse_args
)
if "%~1"=="--detached" (
    set DETACHED=true
    shift
    goto parse_args
)
if "%~1"=="--default-nodes" (
    set WO_DEFAULT_NODES=%~2
    shift
    shift
    goto parse_args
)
if "%~1"=="--settings" (
    set WO_SETTINGS=%~2
    shift
    shift
    goto parse_args
)
if "%~1"=="--worker-memory" (
    set WO_WORKER_MEMORY=%~2
    shift
    shift
    goto parse_args
)
if "%~1"=="--worker-cpus" (
    set WO_WORKER_CPUS=%~2
    shift
    shift
    goto parse_args
)
if "%~1"=="--ipv6" (
    set IPV6=true
    set WO_IPV6=YES
    shift
    goto parse_args
)

REM Store the command for later processing
set COMMAND=%~1
shift
goto parse_args

:end_parse_args

REM Check for docker-compose
where docker-compose >nul 2>&1
if errorlevel 1 (
    where docker >nul 2>&1
    if errorlevel 1 (
        echo Error: Docker is not installed or not in PATH
        echo Please install Docker Desktop from https://www.docker.com/
        exit /b 1
    )
    
    docker compose version >nul 2>&1
    if errorlevel 1 (
        echo Error: docker-compose is not available
        echo Please install Docker Compose or use Docker Desktop
        exit /b 1
    ) else (
        set DOCKER_COMPOSE=docker compose
    )
) else (
    set DOCKER_COMPOSE=docker-compose
)

REM Function to show usage
if "%COMMAND%"=="" goto show_usage
if "%COMMAND%"=="help" goto show_usage
if "%COMMAND%"=="--help" goto show_usage

goto %COMMAND% 2>nul || goto show_usage

:show_usage
echo Usage: %~nx0 ^<command^>
echo.
echo This program helps to manage the setup/teardown of the docker containers for running WebODM.
echo.
echo Command list:
echo     start [options]        Start WebODM
echo     stop                   Stop WebODM
echo     down                   Stop and remove WebODM's docker containers
echo     update                 Update WebODM to the latest release
echo     liveupdate             Update WebODM to the latest release without stopping it
echo     rebuild                Rebuild all docker containers and perform cleanups
echo     checkenv               Do an environment check and install missing components
echo     resetadminpassword     Reset the administrator's password
echo.
echo Options:
echo     --port ^<port^>           Set the port that WebODM should bind to (default: %DEFAULT_PORT%)
echo     --hostname ^<hostname^>   Set the hostname that WebODM will be accessible from (default: %DEFAULT_HOST%)
echo     --media-dir ^<path^>      Path where processing results will be stored (default: %DEFAULT_MEDIA_DIR%)
echo     --db-dir ^<path^>         Path where the Postgres db data will be stored (default: %DEFAULT_DB_DIR%)
echo     --default-nodes ^<num^>   The amount of default NodeODM nodes attached to WebODM (default: %DEFAULT_NODES%)
echo     --with-micmac           Create a NodeMICMAC node attached to WebODM on startup
echo     --ssl                   Enable SSL and automatically request certificate from letsencrypt.org
echo     --ssl-key ^<path^>        Manually specify a path to the private key file (.pem)
echo     --ssl-cert ^<path^>       Manually specify a path to the certificate file (.pem)
echo     --debug                 Enable debug for development environments
echo     --dev                   Enable development mode
echo     --dev-watch-plugins     Automatically build plugins while in dev mode
echo     --broker ^<url^>          Set the URL used to connect to the celery broker (default: %DEFAULT_BROKER%)
echo     --detached              Run WebODM in detached mode (background)
echo     --gpu                   Use GPU NodeODM nodes (experimental on Windows)
echo     --settings ^<path^>       Path to a settings.py file to enable modifications
echo     --worker-memory ^<size^>  Maximum amount of memory allocated for the worker process
echo     --worker-cpus ^<num^>     Maximum number of CPUs allocated for the worker process
echo     --ipv6                  Enable IPV6
exit /b 0

:get_secret
if not exist ".secret_key" (
    echo Generating secret in .secret_key
    REM Generate a random secret key using PowerShell
    powershell -Command "[System.Web.Security.Membership]::GeneratePassword(50, 10)" > .secret_key
)
if exist ".secret_key" (
    set /p WO_SECRET_KEY=<.secret_key
) else (
    set WO_SECRET_KEY=
)
goto :eof

:start
call :get_secret

REM Pre-flight check for Docker issues
echo Performing pre-flight Docker check...
docker run --rm busybox echo "Docker test" >nul 2>&1
if errorlevel 1 (
    echo ERROR: Basic Docker functionality is broken
    echo Please restart Docker Desktop and try again
    exit /b 1
)

REM Check for known exec format error issue
docker run --rm redis:7.0.10 echo "test" >nul 2>&1
if errorlevel 1 (
    echo.
    echo WARNING: Detected "exec format error" issue with Docker containers
    echo This is a known issue with Docker Desktop on Windows
    echo.
    echo RECOMMENDED SOLUTION:
    echo 1. Run: fix-docker-webodm.bat
    echo 2. Or manually reset Docker Desktop to factory defaults
    echo.
    echo Continuing anyway - WebODM may fail to start...
    echo.
    timeout /t 5 /nobreak >nul
)

if "%DEV_MODE%"=="true" (
    echo Starting WebODM in development mode...
    call :down
) else (
    echo Starting WebODM...
)

echo.
echo Using the following environment:
echo ================================
echo Host: %WO_HOST%
echo Port: %WO_PORT%
echo IPv6: %WO_IPV6%
echo Media directory: %WO_MEDIA_DIR%
echo Postgres DB directory: %WO_DB_DIR%
echo SSL: %WO_SSL%
echo SSL key: %WO_SSL_KEY%
echo SSL certificate: %WO_SSL_CERT%
echo SSL insecure port redirect: %WO_SSL_INSECURE_PORT_REDIRECT%
echo Celery Broker: %WO_BROKER%
echo Default Nodes: %WO_DEFAULT_NODES%
echo Settings: %WO_SETTINGS%
echo Worker memory limit: %WO_WORKER_MEMORY%
echo Worker cpus limit: %WO_WORKER_CPUS%
echo ================================
echo Make sure to issue a %~nx0 down if you decide to change the environment.
echo.

set COMPOSE_COMMAND=%DOCKER_COMPOSE% -f docker-compose.yml

if %WO_DEFAULT_NODES% gtr 0 (
    if "%GPU%"=="true" (
        echo Warning: GPU support is experimental on Windows
        set COMPOSE_COMMAND=!COMPOSE_COMMAND! -f docker-compose.nodeodm.yml
    ) else (
        set COMPOSE_COMMAND=!COMPOSE_COMMAND! -f docker-compose.nodeodm.yml
    )
)

if "%LOAD_MICMAC_NODE%"=="true" (
    set COMPOSE_COMMAND=!COMPOSE_COMMAND! -f docker-compose.nodemicmac.yml
)

if "%DEV_MODE%"=="true" (
    set COMPOSE_COMMAND=!COMPOSE_COMMAND! -f docker-compose.dev.yml
)

if "%WO_SSL%"=="YES" (
    if defined WO_SSL_KEY (
        if not exist "%WO_SSL_KEY%" (
            echo SSL key file does not exist: %WO_SSL_KEY%
            exit /b 1
        )
    )
    if defined WO_SSL_CERT (
        if not exist "%WO_SSL_CERT%" (
            echo SSL certificate file does not exist: %WO_SSL_CERT%
            exit /b 1
        )
    )
    
    set COMPOSE_COMMAND=!COMPOSE_COMMAND! -f docker-compose.ssl.yml
    
    set SSL_METHOD=Lets Encrypt
    if defined WO_SSL_KEY if defined WO_SSL_CERT (
        set SSL_METHOD=Manual
        set COMPOSE_COMMAND=!COMPOSE_COMMAND! -f docker-compose.ssl-manual.yml
    )
    
    if "!SSL_METHOD!"=="Lets Encrypt" (
        if not "%WO_PORT%"=="%DEFAULT_PORT%" (
            echo Lets Encrypt cannot run on port: %WO_PORT%, switching to 443.
            echo If you need to use a different port, you'll need to generate the SSL certificate files separately.
        )
        set WO_PORT=443
    )
    
    if "%WO_HOST%"=="localhost" (
        echo SSL is enabled, but hostname cannot be set to %WO_HOST%.
        echo Set the --hostname argument to the domain of your WebODM server.
        exit /b 1
    )
    
    echo Will enable SSL (!SSL_METHOD!)
)

if defined WO_SETTINGS (
    if not exist "%WO_SETTINGS%" (
        echo Settings file does not exist: %WO_SETTINGS%
        exit /b 1
    )
    set COMPOSE_COMMAND=!COMPOSE_COMMAND! -f docker-compose.settings.yml
)

if defined WO_WORKER_MEMORY (
    set COMPOSE_COMMAND=!COMPOSE_COMMAND! -f docker-compose.worker-memory.yml
)

if defined WO_WORKER_CPUS (
    set COMPOSE_COMMAND=!COMPOSE_COMMAND! -f docker-compose.worker-cpu.yml
)

if "%IPV6%"=="true" (
    set COMPOSE_COMMAND=!COMPOSE_COMMAND! -f docker-compose.ipv6.yml
)

set COMPOSE_COMMAND=!COMPOSE_COMMAND! up

if "%DETACHED%"=="true" (
    set COMPOSE_COMMAND=!COMPOSE_COMMAND! -d
)

if %WO_DEFAULT_NODES% gtr 0 (
    set COMPOSE_COMMAND=!COMPOSE_COMMAND! --scale node-odm=%WO_DEFAULT_NODES%
)

echo Running: !COMPOSE_COMMAND!
!COMPOSE_COMMAND!
if errorlevel 1 (
    echo.
    echo ========================================
    echo ERROR: Failed to start WebODM
    echo ========================================
    echo.
    echo Common solutions:
    echo 1. Restart Docker Desktop completely
    echo 2. Run: docker system prune -a --volumes
    echo 3. Check available disk space: docker system df
    echo 4. If using WSL2, run: wsl --shutdown
    echo 5. Check Docker Desktop settings for sufficient RAM/CPU
    echo.
    echo If the error mentions "input/output error" or "failed to extract layer":
    echo - This is usually a Docker storage issue
    echo - Try Docker Desktop Settings ^> Troubleshoot ^> Clean/Purge data
    echo - Or switch storage driver in Docker Engine settings
    echo.
    exit /b 1
)
goto :eof

:stop
echo Stopping WebODM...
set COMPOSE_COMMAND=%DOCKER_COMPOSE% -f docker-compose.yml -f docker-compose.nodeodm.yml -f docker-compose.nodemicmac.yml stop
echo Running: !COMPOSE_COMMAND!
!COMPOSE_COMMAND!
goto :eof

:down
echo Tearing down WebODM...
set COMPOSE_COMMAND=%DOCKER_COMPOSE% -f docker-compose.yml -f docker-compose.nodeodm.yml -f docker-compose.nodemicmac.yml down --remove-orphans
echo Running: !COMPOSE_COMMAND!
!COMPOSE_COMMAND!
goto :eof

:restart
echo Restarting WebODM...
call :down
call :start
goto :eof

:rebuild
echo Rebuilding WebODM...
%DOCKER_COMPOSE% down --remove-orphans
if exist "node_modules" rmdir /s /q node_modules
if exist "nodeodm\external\NodeODM" rmdir /s /q nodeodm\external\NodeODM
%DOCKER_COMPOSE% -f docker-compose.yml -f docker-compose.build.yml build --no-cache
echo Done! You can now start WebODM by running %~nx0 start
goto :eof

:update
echo Updating WebODM...

where git >nul 2>&1
if errorlevel 1 (
    echo Skipping source update (git not found)
) else (
    if exist ".git" (
        echo Running: git pull origin master
        git pull origin master
    ) else (
        echo Skipping source update (.git directory not found)
    )
)

set COMPOSE_COMMAND=%DOCKER_COMPOSE% -f docker-compose.yml

if %WO_DEFAULT_NODES% gtr 0 (
    set COMPOSE_COMMAND=!COMPOSE_COMMAND! -f docker-compose.nodeodm.yml
)

if "%LOAD_MICMAC_NODE%"=="true" (
    set COMPOSE_COMMAND=!COMPOSE_COMMAND! -f docker-compose.nodemicmac.yml
)

set COMPOSE_COMMAND=!COMPOSE_COMMAND! pull
echo Running: !COMPOSE_COMMAND!
!COMPOSE_COMMAND!
goto :eof

:liveupdate
call :update
echo Done! You can now finish the update by running %~nx0 restart
goto :eof

:checkenv
echo Checking environment...
where docker >nul 2>&1
if errorlevel 1 (
    echo ERROR: Docker is not installed or not in PATH
    echo Please install Docker Desktop from https://www.docker.com/
    exit /b 1
)
echo Docker... OK

where docker-compose >nul 2>&1
if errorlevel 1 (
    docker compose version >nul 2>&1
    if errorlevel 1 (
        echo ERROR: docker-compose is not available
        echo Please install Docker Compose or use Docker Desktop
        exit /b 1
    )
    echo Docker Compose ^(plugin^)... OK
) else (
    echo Docker Compose... OK
)

echo Environment check completed successfully!
goto :eof

:resetadminpassword
if "%~2"=="" (
    echo Usage: %~nx0 resetadminpassword "new_password"
    echo The password must be enclosed in double quotes.
    exit /b 1
)

set NEW_PASSWORD=%~2

REM Find the webapp container
for /f "tokens=*" %%i in ('docker ps -q --filter "name=webapp"') do set CONTAINER_HASH=%%i

if not defined CONTAINER_HASH (
    echo Cannot find webapp docker container. Is WebODM running?
    exit /b 1
)

echo Resetting password...
docker exec %CONTAINER_HASH% bash -c "echo \"from django.contrib.auth.models import User;from django.contrib.auth.hashers import make_password;u=User.objects.filter(is_superuser=True)[0];u.password=make_password('%NEW_PASSWORD%');u.save();print('The following user was changed: {}'.format(u.username));\" | python manage.py shell"

if errorlevel 0 (
    echo Password changed successfully!
) else (
    echo Could not change administrator password.
    echo If you need help, please visit https://github.com/OpenDroneMap/WebODM/issues/
    exit /b 1
)
goto :eof

:test
echo Running tests in webapp container...
for /f "tokens=*" %%i in ('docker ps -q --filter "name=webapp"') do set CONTAINER_HASH=%%i

if not defined CONTAINER_HASH (
    echo Cannot find webapp docker container. Is WebODM running?
    exit /b 1
)

set TEST_COMMAND=/webodm/webodm.sh test
docker exec %CONTAINER_HASH% /bin/bash -c "%TEST_COMMAND%"
goto :eof
