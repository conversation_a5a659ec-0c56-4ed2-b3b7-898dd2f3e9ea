@echo off
setlocal enabledelayedexpansion

echo ========================================
echo Starting WebODM in WSL2
echo ========================================
echo.

echo [1/3] Starting Docker service in WSL2...
wsl -d Ubuntu bash -c "sudo service docker start"

echo [2/3] Starting WebODM containers...
wsl -d Ubuntu bash -c "cd /home/<USER>/webodm && export WO_HOST=0.0.0.0 && ./webodm.sh start --detached"

echo [3/3] Checking container status and restarting if needed...
wsl -d Ubuntu bash -c "cd /home/<USER>/webodm && docker ps"

echo.
echo Ensuring all containers are running...
wsl -d Ubuntu bash -c "cd /home/<USER>/webodm && docker start webapp worker db broker webodm_node-odm_1 2>/dev/null || true"

echo.
echo Final container status:
wsl -d Ubuntu bash -c "cd /home/<USER>/webodm && docker ps"

echo.
echo Testing NodeODM connectivity...
wsl -d Ubuntu bash -c "cd /home/<USER>/webodm && timeout 10 bash -c 'until docker exec webapp curl -s http://webodm_node-odm_1:3000/info >/dev/null 2>&1; do echo \"Waiting for NodeODM...\"; sleep 2; done; echo \"NodeODM is ready!\"'"

echo.
echo ========================================
echo WebODM is now running!
echo ========================================
echo.
echo Access WebODM at: http://localhost:8000
echo.
echo To stop WebODM, run: stop-webodm-wsl2.bat
echo To check status, run: wsl -d Ubuntu bash -c "cd /home/<USER>/webodm && docker ps"
echo.
pause
