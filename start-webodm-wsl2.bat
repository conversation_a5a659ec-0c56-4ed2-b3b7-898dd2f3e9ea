@echo off
setlocal enabledelayedexpansion

echo ========================================
echo Starting WebODM in WSL2
echo ========================================
echo.

echo [1/3] Starting Docker service in WSL2...
wsl -d Ubuntu bash -c "sudo service docker start"

echo [2/3] Starting WebODM containers...
wsl -d Ubuntu bash -c "cd /home/<USER>/webodm && export WO_HOST=0.0.0.0 && ./webodm.sh start --detached"

echo [3/3] Checking container status...
wsl -d Ubuntu bash -c "cd /home/<USER>/webodm && docker ps"

echo.
echo ========================================
echo WebODM is now running!
echo ========================================
echo.
echo Access WebODM at: http://localhost:8000
echo.
echo To stop WebODM, run: stop-webodm-wsl2.bat
echo To check status, run: wsl -d Ubuntu bash -c "cd /home/<USER>/webodm && docker ps"
echo.
pause
