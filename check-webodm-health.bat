@echo off
setlocal enabledelayedexpansion

echo ========================================
echo WebODM Health Check
echo ========================================
echo.

echo [1/4] Checking container status...
wsl -d Ubuntu bash -c "cd /home/<USER>/webodm && docker ps --format 'table {{.Names}}\t{{.Status}}\t{{.Ports}}'"

echo.
echo [2/4] Checking if webapp is responding...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:8000' -TimeoutSec 5 -UseBasicParsing; Write-Host 'WebODM webapp: OK (Status:' $response.StatusCode ')' } catch { Write-Host 'WebODM webapp: FAILED -' $_.Exception.Message }"

echo.
echo [3/4] Checking NodeODM connectivity...
wsl -d Ubuntu bash -c "cd /home/<USER>/webodm && docker exec webapp curl -s --connect-timeout 5 http://webodm_node-odm_1:3000/info >/dev/null 2>&1 && echo 'NodeODM connectivity: OK' || echo 'NodeODM connectivity: FAILED'"

echo.
echo [4/4] Checking processing node page...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:8000/processingnode/1/' -TimeoutSec 5 -UseBasicParsing; Write-Host 'Processing node page: OK (Status:' $response.StatusCode ')' } catch { Write-Host 'Processing node page: FAILED -' $_.Exception.Message }"

echo.
echo ========================================
echo Health Check Complete
echo ========================================
echo.

REM Check if any containers are not running
for /f "tokens=*" %%i in ('wsl -d Ubuntu bash -c "cd /home/<USER>/webodm && docker ps -q | wc -l"') do set RUNNING_COUNT=%%i

if %RUNNING_COUNT% LSS 5 (
    echo WARNING: Only %RUNNING_COUNT% containers running, expected 5
    echo.
    echo Would you like to restart WebODM? ^(y/N^)
    set /p RESTART_CHOICE=
    if /i "!RESTART_CHOICE!"=="y" (
        echo.
        echo Restarting WebODM...
        start-webodm-wsl2.bat
    )
) else (
    echo All containers are running properly!
)

echo.
pause
