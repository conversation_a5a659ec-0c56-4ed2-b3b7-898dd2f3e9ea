@echo off
setlocal enabledelayedexpansion

REM Docker Troubleshooting Script for WebODM on Windows
echo ========================================
echo Docker Troubleshooting Script for WebODM
echo ========================================
echo.

REM Check if Docker is running
echo [1/6] Checking if Dock<PERSON> is running...
docker version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Docker is not running or not accessible
    echo Please start Docker Desktop and try again
    pause
    exit /b 1
)
echo Docker is running ✓
echo.

REM Check Docker disk usage
echo [2/6] Checking Docker disk usage...
echo.
docker system df
echo.

REM Check available disk space on system
echo [3/6] Checking system disk space...
for /f "tokens=3" %%a in ('dir /-c %SystemDrive%\ ^| find "bytes free"') do set FREE_SPACE=%%a
echo Free space on %SystemDrive%: %FREE_SPACE% bytes
echo.

REM Check Docker Desktop settings
echo [4/6] Checking Docker containers and images...
echo.
echo Current containers:
docker ps -a
echo.
echo Current images:
docker images
echo.

REM Check for WebODM specific containers
echo [5/6] Checking for existing WebODM containers...
docker ps -a --filter "name=webapp" --filter "name=db" --filter "name=broker" --filter "name=worker"
echo.

REM Provide recommendations
echo [6/6] Recommendations:
echo.

REM Check if there are stopped WebODM containers
for /f %%i in ('docker ps -aq --filter "name=webapp"') do set WEBAPP_EXISTS=%%i
for /f %%i in ('docker ps -aq --filter "name=db"') do set DB_EXISTS=%%i

if defined WEBAPP_EXISTS (
    echo Found existing WebODM containers.
    echo.
    echo RECOMMENDED ACTIONS:
    echo 1. Clean up existing containers: webodm.bat down
    echo 2. Then try starting again: webodm.bat start
    echo.
) else (
    echo No existing WebODM containers found.
    echo.
)

REM Check for common issues
echo TROUBLESHOOTING STEPS for "failed to extract layer" errors:
echo.
echo STEP 1 - Quick fixes:
echo   docker system prune -f
echo   webodm.bat start
echo.
echo STEP 2 - If Step 1 fails, restart Docker:
echo   1. Right-click Docker Desktop tray icon
echo   2. Select "Quit Docker Desktop"
echo   3. Wait 30 seconds
echo   4. Start Docker Desktop again
echo   5. Run: webodm.bat start
echo.
echo STEP 3 - If Step 2 fails, clean Docker completely:
echo   docker system prune -a --volumes
echo   ^(WARNING: This removes ALL Docker data^)
echo   webodm.bat start
echo.
echo STEP 4 - If using WSL2 backend:
echo   wsl --shutdown
echo   ^(Then restart Docker Desktop^)
echo.
echo STEP 5 - Reset Docker Desktop ^(last resort^):
echo   1. Docker Desktop Settings ^> Troubleshoot
echo   2. Click "Clean / Purge data"
echo   3. This resets Docker to factory defaults
echo.
echo STEP 6 - Check Docker Desktop resource allocation:
echo   1. Docker Desktop Settings ^> Resources
echo   2. Ensure at least 4GB RAM allocated
echo   3. Ensure at least 2 CPUs allocated
echo   4. Check disk space allocation
echo.

echo ========================================
echo.
echo Would you like to run automatic cleanup? ^(y/n^)
set /p CLEANUP_CHOICE=
if /i "%CLEANUP_CHOICE%"=="y" (
    echo.
    echo Running Docker cleanup...
    echo Stopping any running WebODM containers...
    docker-compose -f docker-compose.yml -f docker-compose.nodeodm.yml -f docker-compose.nodemicmac.yml down --remove-orphans 2>nul
    
    echo Cleaning Docker system...
    docker system prune -f
    
    echo Cleanup completed!
    echo.
    echo You can now try: webodm.bat start
) else (
    echo.
    echo Manual cleanup skipped.
    echo Please follow the troubleshooting steps above if needed.
)

echo.
echo ========================================
echo Troubleshooting script completed.
echo ========================================
pause
