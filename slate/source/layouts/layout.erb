<%#
Copyright 2008-2013 Concur Technologies, Inc.

Licensed under the Apache License, Version 2.0 (the "License"); you may
not use this file except in compliance with the License. You may obtain
a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
%>
<% language_tabs = current_page.data.language_tabs || [] %>
<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title><%= current_page.data.title || "API Documentation" %></title>

    <style>
      <%= Rouge::Themes::Base16::Monokai.render(:scope => '.highlight') %>
    </style>
    <%= stylesheet_link_tag :screen, media: :screen %>
    <%= stylesheet_link_tag :print, media: :print %>
    <% if current_page.data.search %>
      <%= javascript_include_tag  "all" %>
    <% else %>
      <%= javascript_include_tag  "all_nosearch" %>
    <% end %>
  </head>

  <body class="<%= page_classes %>" data-languages="<%=h language_tabs.map{ |lang| lang.is_a?(Hash) ? lang.keys.first : lang }.to_json %>">
    <a href="#" id="nav-button">
      <span>
        NAV
        <%= image_tag('navbar.png') %>
      </span>
    </a>
    <div class="tocify-wrapper">
      <%= image_tag "logo.png" %>
      <% if language_tabs.any? %>
        <div class="lang-selector">
          <% language_tabs.each do |lang| %>
            <% if lang.is_a? Hash %>
              <a href="#" data-language-name="<%= lang.keys.first %>"><%= lang.values.first %></a>
            <% else %>
              <a href="#" data-language-name="<%= lang %>"><%= lang %></a>
            <% end %>
          <% end %>
        </div>
      <% end %>
      <% if current_page.data.search %>
        <div class="search">
          <input type="text" class="search" id="input-search" placeholder="Search">
        </div>
        <ul class="search-results"></ul>
      <% end %>
      <div id="toc">
      </div>
      <% if current_page.data.toc_footers %>
        <ul class="toc-footer">
          <% current_page.data.toc_footers.each do |footer| %>
            <li><%= footer %></li>
          <% end %>
        </ul>
      <% end %>
    </div>
    <div class="page-wrapper">
      <div class="dark-box"></div>
      <div class="content">
        <%= yield %>
        <% current_page.data.includes && current_page.data.includes.each do |include| %>
          <%= partial "includes/#{include}" %>
        <% end %>
      </div>
      <div class="dark-box">
        <% if language_tabs.any? %>
          <div class="lang-selector">
            <% language_tabs.each do |lang| %>
              <% if lang.is_a? Hash %>
                <a href="#" data-language-name="<%= lang.keys.first %>"><%= lang.values.first %></a>
              <% else %>
                <a href="#" data-language-name="<%= lang %>"><%= lang %></a>
              <% end %>
            <% end %>
          </div>
        <% end %>
      </div>
    </div>
  </body>
</html>
