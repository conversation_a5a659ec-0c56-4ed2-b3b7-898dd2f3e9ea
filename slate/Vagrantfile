Vagrant.configure(2) do |config|
  config.vm.box = "ubuntu/trusty64"
  config.vm.network :forwarded_port, guest: 4567, host: 4567

  config.vm.provision "bootstrap",
    type: "shell",
    privileged: false,
    inline: <<-SHELL
      sudo apt-get update
      sudo apt-get install -yq git gcc build-essential libreadline-dev zlib1g-dev pkg-config nodejs libxml2-dev libxslt-dev
      sudo apt-get install -yq libssl-dev
      sudo apt-get autoremove -yq
      git clone https://github.com/sstephenson/rbenv.git /home/<USER>/.rbenv
      echo 'export PATH="$HOME/.rbenv/bin:$PATH"' >> /home/<USER>/.bash_profile
      echo 'eval "$(rbenv init -)"' >> /home/<USER>/.bash_profile
      source /home/<USER>/.bash_profile
      git clone https://github.com/sstephenson/ruby-build.git /home/<USER>/.rbenv/plugins/ruby-build
      cd /home/<USER>
      rbenv install 2.4.7
      rbenv global 2.4.7
      gem install --no-ri --no-rdoc bundler
    SHELL

  # add the local user git config to the vm
  config.vm.provision "file", source: "~/.gitconfig", destination: ".gitconfig"

  config.vm.provision "install",
    type: "shell",
    privileged: false,
    inline: <<-SHELL
      echo "=============================================="
      echo "Installing app dependencies"
      cd /vagrant
      bundle config build.nokogiri --use-system-libraries
      bundle install
    SHELL

  config.vm.provision "run",
    type: "shell",
    privileged: false,
    run: "always",
    inline: <<-SHELL
      echo "=============================================="
      echo "Starting up middleman at http://localhost:4567"
      echo "If it does not come up, check the ~/middleman.log file for any error messages"
      cd /vagrant
      bundle exec middleman server --watcher-force-polling --watcher_latency=1 &> ~/middleman.log &
    SHELL
end
