GIT
  remote: https://github.com/middleman/middleman.git
  revision: d180ca337202873f2601310c74ba2b6b4cf063ec
  branch: 4.x
  specs:
    middleman (4.3.11)
      coffee-script (~> 2.2)
      haml (>= 4.0.5)
      kramdown (>= 2.3.0)
      middleman-cli (= 4.3.11)
      middleman-core (= 4.3.11)
    middleman-cli (4.3.11)
      thor (>= 0.17.0, < 2.0)
    middleman-core (4.3.11)
      activesupport (>= 4.2, < 6.1)
      addressable (~> 2.3)
      backports (~> 3.6)
      bundler (~> 2.0)
      contracts (~> 0.13.0)
      dotenv
      erubis
      execjs (~> 2.0)
      fast_blank
      fastimage (~> 2.0)
      hamster (~> 3.0)
      hashie (~> 3.4)
      i18n (~> 0.9.0)
      listen (~> 3.0.0)
      memoist (~> 0.14)
      padrino-helpers (~> 0.13.0)
      parallel
      rack (>= 1.4.5, < 3)
      sassc (~> 2.0)
      servolux
      tilt (~> 2.0.9)
      toml
      uglifier (~> 3.0)
      webrick

GEM
  remote: https://rubygems.org/
  specs:
    activesupport (*******)
      concurrent-ruby (~> 1.0, >= 1.0.2)
      i18n (>= 0.7, < 2)
      minitest (~> 5.1)
      tzinfo (~> 1.1)
      zeitwerk (~> 2.2, >= 2.2.2)
    addressable (2.7.0)
      public_suffix (>= 2.0.2, < 5.0)
    autoprefixer-rails (*******)
      execjs
    backports (3.21.0)
    coffee-script (2.4.1)
      coffee-script-source
      execjs
    coffee-script-source (1.12.2)
    concurrent-ruby (1.1.8)
    contracts (0.13.0)
    dotenv (2.7.6)
    em-websocket (0.5.2)
      eventmachine (>= 0.12.9)
      http_parser.rb (~> 0.6.0)
    erubis (2.7.0)
    eventmachine (1.2.7)
    eventmachine (1.2.7-x64-mingw32)
    execjs (2.7.0)
    fast_blank (1.0.0)
    fastimage (2.2.3)
    ffi (1.15.0)
    ffi (1.15.0-x64-mingw32)
    haml (5.2.1)
      temple (>= 0.8.0)
      tilt
    hamster (3.0.0)
      concurrent-ruby (~> 1.0)
    hashie (3.6.0)
    http_parser.rb (0.6.0)
    i18n (0.9.5)
      concurrent-ruby (~> 1.0)
    kramdown (2.3.1)
      rexml
    listen (3.0.8)
      rb-fsevent (~> 0.9, >= 0.9.4)
      rb-inotify (~> 0.9, >= 0.9.7)
    memoist (0.16.2)
    middleman-autoprefixer (2.10.1)
      autoprefixer-rails (~> 9.1)
      middleman-core (>= 3.3.3)
    middleman-livereload (3.4.7)
      em-websocket (~> 0.5.1)
      middleman-core (>= 3.3)
      rack-livereload (~> 0.3.15)
    middleman-sprockets (4.1.1)
      middleman-core (~> 4.0)
      sprockets (>= 3.0)
    middleman-syntax (3.2.0)
      middleman-core (>= 3.2)
      rouge (~> 3.2)
    mini_portile2 (2.5.0)
    minitest (5.14.4)
    nokogiri (1.11.3)
      mini_portile2 (~> 2.5.0)
      racc (~> 1.4)
    nokogiri (1.11.3-x64-mingw32)
      racc (~> 1.4)
    padrino-helpers (0.13.3.4)
      i18n (~> 0.6, >= 0.6.7)
      padrino-support (= 0.13.3.4)
      tilt (>= 1.4.1, < 3)
    padrino-support (0.13.3.4)
      activesupport (>= 3.1)
    parallel (1.20.1)
    parslet (1.8.2)
    public_suffix (4.0.6)
    racc (1.5.2)
    rack (2.2.3)
    rack-livereload (0.3.17)
      rack
    rb-fsevent (0.10.4)
    rb-inotify (0.10.1)
      ffi (~> 1.0)
    redcarpet (3.5.1)
    rexml (3.2.5)
    rouge (3.26.0)
    sass (3.7.4)
      sass-listen (~> 4.0.0)
    sass-listen (4.0.0)
      rb-fsevent (~> 0.9, >= 0.9.4)
      rb-inotify (~> 0.9, >= 0.9.7)
    sassc (2.4.0)
      ffi (~> 1.9)
    sassc (2.4.0-x64-mingw32)
      ffi (~> 1.9)
    servolux (0.13.0)
    sprockets (3.7.2)
      concurrent-ruby (~> 1.0)
      rack (> 1, < 3)
    temple (0.8.2)
    thor (1.1.0)
    thread_safe (0.3.6)
    tilt (2.0.10)
    toml (0.2.0)
      parslet (~> 1.8.0)
    tzinfo (1.2.9)
      thread_safe (~> 0.1)
    uglifier (3.2.0)
      execjs (>= 0.3.0, < 3)
    webrick (1.7.0)
    zeitwerk (2.4.2)

PLATFORMS
  ruby
  x64-mingw32

DEPENDENCIES
  middleman!
  middleman-autoprefixer (~> 2.7)
  middleman-livereload
  middleman-sprockets (~> 4.1)
  middleman-syntax (~> 3.2)
  nokogiri (~> 1.11.0)
  redcarpet (~> 3.5.0)
  rouge (~> 3.21)
  sass
  webrick

RUBY VERSION
   ruby 2.7.2p137

BUNDLED WITH
   2.2.19
