@echo off
setlocal enabledelayedexpansion

echo ========================================
echo WebODM Issue Theory Testing
echo ========================================
echo.
echo This script tests various theories about why the processing node
echo page becomes intermittently unavailable.
echo.

echo [THEORY 1] Testing if it's a timing/startup issue...
echo =====================================================
echo.

echo Checking container startup times...
wsl -d Ubuntu bash -c "cd /home/<USER>/webodm && docker ps --format 'table {{.Names}}\t{{.Status}}\t{{.Ports}}'"

echo.
echo Testing multiple rapid requests to see if there's a pattern...
for /L %%i in (1,1,10) do (
    powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:8000/processingnode/1/' -TimeoutSec 2 -UseBasicParsing; Write-Host 'Request %%i: OK (' $response.StatusCode ')' } catch { Write-Host 'Request %%i: FAILED -' $_.Exception.Message }"
    timeout /t 1 /nobreak >nul
)

echo.
echo [THEORY 2] Testing if it's a database connection issue...
echo ========================================================
echo.

echo Testing database connectivity from webapp...
wsl -d Ubuntu bash -c "cd /home/<USER>/webodm && docker exec webapp python manage.py shell -c 'from django.db import connection; connection.ensure_connection(); print(\"Database connection: OK\")' 2>/dev/null || echo 'Database connection: FAILED'"

echo.
echo Checking database logs for connection issues...
wsl -d Ubuntu bash -c "cd /home/<USER>/webodm && docker logs db --since 60s 2>&1 | grep -i -E '(connection|error|fail)' | tail -5"

echo.
echo [THEORY 3] Testing if it's a NodeODM communication issue...
echo ==========================================================
echo.

echo Testing NodeODM container health...
wsl -d Ubuntu bash -c "cd /home/<USER>/webodm && docker exec webodm_node-odm_1 curl -s http://localhost:3000/info | head -1"

echo.
echo Testing webapp to NodeODM connectivity...
wsl -d Ubuntu bash -c "cd /home/<USER>/webodm && docker exec webapp curl -s --connect-timeout 5 http://webodm_node-odm_1:3000/info | head -1"

echo.
echo Testing network connectivity between containers...
wsl -d Ubuntu bash -c "cd /home/<USER>/webodm && docker exec webapp ping -c 3 webodm_node-odm_1"

echo.
echo [THEORY 4] Testing if it's a gunicorn/nginx issue...
echo ===================================================
echo.

echo Checking nginx status inside webapp container...
wsl -d Ubuntu bash -c "cd /home/<USER>/webodm && docker exec webapp ps aux | grep nginx"

echo.
echo Checking gunicorn workers...
wsl -d Ubuntu bash -c "cd /home/<USER>/webodm && docker exec webapp ps aux | grep gunicorn | wc -l"

echo.
echo Testing direct gunicorn socket...
wsl -d Ubuntu bash -c "cd /home/<USER>/webodm && docker exec webapp test -S /tmp/gunicorn.sock && echo 'Gunicorn socket: OK' || echo 'Gunicorn socket: MISSING'"

echo.
echo [THEORY 5] Testing if it's a resource/memory issue...
echo ====================================================
echo.

echo Checking memory usage...
wsl -d Ubuntu bash -c "free -h"

echo.
echo Checking container resource usage...
wsl -d Ubuntu bash -c "cd /home/<USER>/webodm && docker stats --no-stream --format 'table {{.Name}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}'"

echo.
echo Checking for OOM kills...
wsl -d Ubuntu bash -c "dmesg | grep -i 'killed process' | tail -3"

echo.
echo [THEORY 6] Testing if it's a Docker networking issue...
echo ======================================================
echo.

echo Checking Docker network configuration...
wsl -d Ubuntu bash -c "cd /home/<USER>/webodm && docker network ls"

echo.
echo Checking container network connectivity...
wsl -d Ubuntu bash -c "cd /home/<USER>/webodm && docker network inspect webodm_default | grep -A 5 -B 5 'webapp\|webodm_node-odm_1'"

echo.
echo [THEORY 7] Testing if it's a port forwarding issue...
echo ====================================================
echo.

echo Checking port bindings...
wsl -d Ubuntu bash -c "cd /home/<USER>/webodm && docker port webapp"

echo.
echo Testing localhost vs 127.0.0.1 vs WSL IP...
set WSL_IP=
for /f "tokens=*" %%i in ('wsl -d Ubuntu bash -c "hostname -I | awk '{print $1}'"') do set WSL_IP=%%i

echo WSL2 IP: %WSL_IP%

powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:8000' -TimeoutSec 2 -UseBasicParsing; Write-Host 'localhost:8000 - OK (' $response.StatusCode ')' } catch { Write-Host 'localhost:8000 - FAILED' }"

powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://127.0.0.1:8000' -TimeoutSec 2 -UseBasicParsing; Write-Host '127.0.0.1:8000 - OK (' $response.StatusCode ')' } catch { Write-Host '127.0.0.1:8000 - FAILED' }"

powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://%WSL_IP%:8000' -TimeoutSec 2 -UseBasicParsing; Write-Host '%WSL_IP%:8000 - OK (' $response.StatusCode ')' } catch { Write-Host '%WSL_IP%:8000 - FAILED' }"

echo.
echo [SUMMARY] Theory Testing Complete
echo =================================
echo.
echo The detailed monitoring script (webodm-detailed-monitor.bat) can be run
echo to continuously track these metrics and identify patterns.
echo.
pause
