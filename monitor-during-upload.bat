@echo off
setlocal enabledelayedexpansion

echo ========================================
echo WebODM Upload Monitoring Script
echo ========================================
echo.
echo INSTRUCTIONS:
echo 1. Run this script
echo 2. Go to WebODM in your browser
echo 3. Start uploading images and GCP files
echo 4. Watch this console for real-time monitoring
echo.
echo This script will monitor system resources and connectivity
echo while you perform the upload that causes crashes.
echo.
echo Press Ctrl+C to stop monitoring.
echo.

set LOGFILE=upload-monitoring-%date:~-4,4%%date:~-10,2%%date:~-7,2%-%time:~0,2%%time:~3,2%%time:~6,2%.log
set LOGFILE=%LOGFILE: =0%

echo Starting upload monitoring at %date% %time% > %LOGFILE%
echo ======================================================== >> %LOGFILE%

echo Ready to monitor. Start your upload process now...
echo.

:monitor_loop
set TIMESTAMP=%date% %time%

REM Quick status display
echo [%TIMESTAMP%] Monitoring...

REM Test processing node availability
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:8000/processingnode/1/' -TimeoutSec 2 -UseBasicParsing; Write-Host '  Processing Node: OK' -ForegroundColor Green } catch { Write-Host '  Processing Node: FAILED - ' $_.Exception.Message -ForegroundColor Red; Write-Output '[%TIMESTAMP%] PROCESSING_NODE_FAILED: ' $_.Exception.Message >> %LOGFILE% }"

REM Check container status
for /f "tokens=*" %%i in ('wsl -d Ubuntu bash -c "cd /home/<USER>/webodm && docker ps -q | wc -l"') do set CONTAINER_COUNT=%%i
if %CONTAINER_COUNT% LSS 5 (
    echo   WARNING: Only %CONTAINER_COUNT%/5 containers running!
    echo [%TIMESTAMP%] CONTAINER_COUNT_LOW: %CONTAINER_COUNT%/5 >> %LOGFILE%
)

REM Monitor memory usage
for /f "tokens=2" %%i in ('wsl -d Ubuntu bash -c "cd /home/<USER>/webodm && docker stats --no-stream webapp --format '{{.MemPerc}}'"') do set WEBAPP_MEM=%%i
echo   WebApp Memory: %WEBAPP_MEM%

REM Log detailed stats every cycle
echo [%TIMESTAMP%] === DETAILED STATS === >> %LOGFILE%
wsl -d Ubuntu bash -c "cd /home/<USER>/webodm && docker stats --no-stream --format '{{.Name}}: CPU={{.CPUPerc}} MEM={{.MemUsage}} ({{.MemPerc}})'" >> %LOGFILE%

REM Check for recent errors in webapp logs
wsl -d Ubuntu bash -c "cd /home/<USER>/webodm && docker logs webapp --since 5s 2>&1 | grep -i -E '(error|exception|fail|timeout|killed|memory)'" >> %LOGFILE% 2>&1

REM Check for container restarts
wsl -d Ubuntu bash -c "cd /home/<USER>/webodm && docker events --since '5s' --until '0s' --format '{{.Time}}: {{.Action}} {{.Actor.Attributes.name}}'" >> %LOGFILE%

REM Check system memory
wsl -d Ubuntu bash -c "free | grep Mem | awk '{printf \"SYSTEM_MEM: %.1f%% used\\n\", \$3/\$2*100}'" >> %LOGFILE%

REM Test NodeODM connectivity
wsl -d Ubuntu bash -c "cd /home/<USER>/webodm && timeout 3 docker exec webapp curl -s --connect-timeout 2 http://webodm_node-odm_1:3000/info >/dev/null 2>&1 && echo '[%TIMESTAMP%] NODEODM_CONN: OK' || echo '[%TIMESTAMP%] NODEODM_CONN: FAILED'" >> %LOGFILE%

REM Check for database issues
wsl -d Ubuntu bash -c "cd /home/<USER>/webodm && timeout 3 docker exec webapp python manage.py shell -c 'from django.db import connection; connection.ensure_connection(); print(\"[%TIMESTAMP%] DB_CONN: OK\")' 2>/dev/null || echo '[%TIMESTAMP%] DB_CONN: FAILED'" >> %LOGFILE%

echo ================================================== >> %LOGFILE%

REM Wait 3 seconds before next check (faster monitoring during upload)
timeout /t 3 /nobreak >nul
goto monitor_loop
