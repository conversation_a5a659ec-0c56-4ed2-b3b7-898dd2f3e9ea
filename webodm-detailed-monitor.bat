@echo off
setlocal enabledelayedexpansion

echo ========================================
echo WebODM Detailed Monitor and Logger
echo ========================================
echo.
echo This script will continuously monitor WebODM and log detailed information
echo about connectivity issues to help diagnose the intermittent problems.
echo.
echo Press Ctrl+C to stop monitoring.
echo.

REM Create log file with timestamp
set LOGFILE=webodm-monitor-%date:~-4,4%%date:~-10,2%%date:~-7,2%-%time:~0,2%%time:~3,2%%time:~6,2%.log
set LOGFILE=%LOGFILE: =0%

echo Starting detailed monitoring at %date% %time% > %LOGFILE%
echo Log file: %LOGFILE%
echo.

:monitor_loop
set TIMESTAMP=%date% %time%
echo [%TIMESTAMP%] === MONITORING CYCLE ===

REM Test 1: Basic container status
echo [%TIMESTAMP%] Checking container status... >> %LOGFILE%
for /f "tokens=*" %%i in ('wsl -d Ubuntu bash -c "cd /home/<USER>/webodm && docker ps --format '{{.Names}}: {{.Status}}'"') do (
    echo [%TIMESTAMP%] Container: %%i >> %LOGFILE%
)

REM Test 2: WebODM main page
echo [%TIMESTAMP%] Testing WebODM main page... >> %LOGFILE%
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:8000' -TimeoutSec 3 -UseBasicParsing; Write-Output 'MAIN_PAGE_OK:' $response.StatusCode } catch { Write-Output 'MAIN_PAGE_FAIL:' $_.Exception.Message }" >> %LOGFILE% 2>&1

REM Test 3: Processing node page
echo [%TIMESTAMP%] Testing processing node page... >> %LOGFILE%
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:8000/processingnode/1/' -TimeoutSec 3 -UseBasicParsing; Write-Output 'NODE_PAGE_OK:' $response.StatusCode } catch { Write-Output 'NODE_PAGE_FAIL:' $_.Exception.Message }" >> %LOGFILE% 2>&1

REM Test 4: NodeODM direct connectivity from webapp
echo [%TIMESTAMP%] Testing NodeODM connectivity from webapp... >> %LOGFILE%
wsl -d Ubuntu bash -c "cd /home/<USER>/webodm && timeout 5 docker exec webapp curl -s --connect-timeout 3 http://webodm_node-odm_1:3000/info >/dev/null 2>&1 && echo 'NODEODM_CONN_OK' || echo 'NODEODM_CONN_FAIL'" >> %LOGFILE%

REM Test 5: NodeODM direct access
echo [%TIMESTAMP%] Testing NodeODM direct access... >> %LOGFILE%
wsl -d Ubuntu bash -c "cd /home/<USER>/webodm && timeout 5 curl -s --connect-timeout 3 http://localhost:3000/info >/dev/null 2>&1 && echo 'NODEODM_DIRECT_OK' || echo 'NODEODM_DIRECT_FAIL'" >> %LOGFILE%

REM Test 6: Database connectivity
echo [%TIMESTAMP%] Testing database connectivity... >> %LOGFILE%
wsl -d Ubuntu bash -c "cd /home/<USER>/webodm && timeout 5 docker exec webapp python manage.py shell -c 'from django.db import connection; connection.ensure_connection(); print(\"DB_CONN_OK\")' 2>/dev/null || echo 'DB_CONN_FAIL'" >> %LOGFILE%

REM Test 7: Check for recent container events
echo [%TIMESTAMP%] Checking recent container events... >> %LOGFILE%
wsl -d Ubuntu bash -c "cd /home/<USER>/webodm && docker events --since '30s' --until '0s' --format '{{.Time}}: {{.Action}} {{.Actor.Attributes.name}}'" >> %LOGFILE%

REM Test 8: Memory and resource usage
echo [%TIMESTAMP%] Checking resource usage... >> %LOGFILE%
wsl -d Ubuntu bash -c "cd /home/<USER>/webodm && docker stats --no-stream --format '{{.Name}}: CPU={{.CPUPerc}} MEM={{.MemUsage}}'" >> %LOGFILE%

REM Test 9: Check webapp logs for errors
echo [%TIMESTAMP%] Checking webapp logs for recent errors... >> %LOGFILE%
wsl -d Ubuntu bash -c "cd /home/<USER>/webodm && docker logs webapp --since 30s 2>&1 | grep -i -E '(error|exception|fail|timeout)' | tail -3" >> %LOGFILE%

REM Display summary to console
echo [%TIMESTAMP%] Quick Status Check:
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:8000/processingnode/1/' -TimeoutSec 2 -UseBasicParsing; Write-Host '  Processing Node: OK (' $response.StatusCode ')' -ForegroundColor Green } catch { Write-Host '  Processing Node: FAILED -' $_.Exception.Message -ForegroundColor Red }"

wsl -d Ubuntu bash -c "cd /home/<USER>/webodm && timeout 3 docker exec webapp curl -s --connect-timeout 2 http://webodm_node-odm_1:3000/info >/dev/null 2>&1 && echo '  NodeODM Connectivity: OK' || echo '  NodeODM Connectivity: FAILED'"

for /f "tokens=*" %%i in ('wsl -d Ubuntu bash -c "cd /home/<USER>/webodm && docker ps -q | wc -l"') do set CONTAINER_COUNT=%%i
echo   Containers Running: %CONTAINER_COUNT%/5

echo.
echo [%TIMESTAMP%] Cycle complete. Waiting 15 seconds... >> %LOGFILE%
echo ================================================== >> %LOGFILE%

REM Wait 15 seconds before next check
timeout /t 15 /nobreak >nul
goto monitor_loop
