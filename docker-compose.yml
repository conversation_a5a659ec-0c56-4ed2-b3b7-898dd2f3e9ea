# This configuration does not include a processing node
# Which makes for faster setup times
version: '2.1'
volumes:
  dbdata:
  appmedia:
services:
  db:
    image: opendronemap/webodm_db
    container_name: db
    expose:
      - "5432"
    volumes:
      - ${WO_DB_DIR}:/var/lib/postgresql/data:Z
    restart: unless-stopped
    oom_score_adj: -100
  webapp:
    image: opendronemap/webodm_webapp
    container_name: webapp
    entrypoint: /bin/bash -c "service cron start && chmod +x /webodm/*.sh && /bin/bash -c \"/webodm/wait-for-postgres.sh db /webodm/wait-for-it.sh -t 0 broker:6379 -- /webodm/start.sh\""
    volumes:
      - ${WO_MEDIA_DIR}:/webodm/app/media:z
    ports:
      - "${WO_PORT}:8000"
    depends_on:
      - db
      - broker
      - worker
    environment:
      - WO_PORT
      - WO_HOST
      - WO_DEBUG
      - WO_BROKER
      - WO_DEV
      - WO_DEV_WATCH_PLUGINS
      - WO_SECRET_KEY
      - WEB_CONCURRENCY
    restart: unless-stopped
    oom_score_adj: 0
  broker:
    image: redis:7.0.10
    container_name: broker
    restart: unless-stopped
    oom_score_adj: -500
  worker:
    image: opendronemap/webodm_webapp
    container_name: worker
    entrypoint: /bin/bash -c "/webodm/wait-for-postgres.sh db /webodm/wait-for-it.sh -t 0 broker:6379 -- /webodm/wait-for-it.sh -t 0 webapp:8000 -- /webodm/worker.sh start"
    volumes:
      - ${WO_MEDIA_DIR}:/webodm/app/media:z
    depends_on:
      - db
      - broker
    environment:
      - WO_BROKER
      - WO_DEBUG
      - WO_SECRET_KEY
      - WEB_CONCURRENCY
    restart: unless-stopped
    oom_score_adj: 250
